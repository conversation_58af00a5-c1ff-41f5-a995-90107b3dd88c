<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.refund.mapper.compareBillDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="compareBillDetailResultMap" type="org.springblade.refund.entity.compareBillDetail">
        <id column="id" property="id"/>
        <result column="main_order_code" property="mainOrderCode"/>
        <result column="chinaums_business_order_no" property="chinaumsBusinessOrderNo"/>
        <result column="chinaums_trans_date" property="chinaumsTransDate"/>
        <result column="chinaums_pay_amount" property="chinaumsPayAmount"/>
        <result column="ydpt_business_order_no" property="ydptBusinessOrderNo"/>
        <result column="ydpt_trans_date" property="ydptTransDate"/>
        <result column="ydpt_pay_amount" property="ydptPayAmount"/>
        <result column="nypt_business_order_no" property="nyptBusinessOrderNo"/>
        <result column="nypt_trans_date" property="nyptTransDate"/>
        <result column="nypt_pay_amount" property="nyptPayAmount"/>
        <result column="nebula_business_order_no" property="nebulaBusinessOrderNo"/>
        <result column="nebula_trans_date" property="nebulaTransDate"/>
        <result column="nebula_pay_amount" property="nebulaPayAmount"/>
        <result column="etc_business_order_no" property="etcBusinessOrderNo"/>
        <result column="etc_trans_date" property="etcTransDate"/>
        <result column="etc_pay_amount" property="etcPayAmount"/>
        <result column="balance_status" property="balanceStatus"/>
        <result column="balance_person" property="balancePerson"/>
        <result column="balance_time" property="balanceTime"/>
        <result column="trans_type" property="transType"/>
        <result column="compare_type" property="compareType"/>
        <result column="compare_result" property="compareResult"/>
        <result column="version_no" property="versionNo"/>
        <result column="funds_from" property="fundsFrom"/>
        <result column="operator_id" property="operatorId"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectCompareBillDetailPage" resultMap="compareBillDetailResultMap">
        select * from compare_bill_detail where is_deleted = 0
        <if test="compareBillDetail.operatorId != null and compareBillDetail.operatorId != ''">
            and operator_id = #{compareBillDetail.operatorId}
        </if>
        <if test="compareBillDetail.fundsFrom != null">
            and funds_from = #{compareBillDetail.fundsFrom}
        </if>
        <if test="compareBillDetail.versionNo != null and compareBillDetail.versionNo != ''">
            and version_no = #{compareBillDetail.versionNo}
        </if>
        <if test="compareBillDetail.versionNoStart != null and compareBillDetail.versionNoStart != ''">
            and version_no &gt;= #{compareBillDetail.versionNoStart}
        </if>
        <if test="compareBillDetail.versionNoEnd != null and compareBillDetail.versionNoEnd != ''">
            and version_no &lt;= #{compareBillDetail.versionNoEnd}
        </if>
        <if test="compareBillDetail.compareResult != null and compareBillDetail.compareResult != ''">
            and compare_result = #{compareBillDetail.compareResult}
        </if>
        <if test="compareBillDetail.transType != null and compareBillDetail.transType != ''">
            and trans_type = #{compareBillDetail.transType}
        </if>
        <if test="compareBillDetail.mainOrderCode != null and compareBillDetail.mainOrderCode != ''">
            and main_order_code like concat('%', #{compareBillDetail.mainOrderCode}, '%')
        </if>
        <if test="compareBillDetail.nyptBusinessOrderNo != null and compareBillDetail.nyptBusinessOrderNo != ''">
            and nypt_business_order_no like concat('%', #{compareBillDetail.nyptBusinessOrderNo}, '%')
        </if>
        <if test="compareBillDetail.chinaumsBusinessOrderNo != null and compareBillDetail.chinaumsBusinessOrderNo != ''">
            and chinaums_business_order_no like concat('%', #{compareBillDetail.chinaumsBusinessOrderNo}, '%')
        </if>
    </select>

</mapper>