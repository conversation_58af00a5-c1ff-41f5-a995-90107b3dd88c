/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service;

import org.springblade.system.entity.NyptPayBill;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface INyptPayBillService extends BaseService<NyptPayBill> {

    /**
     * 获取指定日期的支付对账明细列表
     *
     * @param date 交易日期
     * @return 支付对账明细列表
     */
    List<NyptPayBill> listByDate(Date date);

    /**
     * 根据订单号获取支付对账明细
     *
     * @param mainOrderCode 充电订单号
     * @return 支付对账明细
     */
    NyptPayBill getByOrderNo(String mainOrderCode);

    /**
     * 生成对账数据
     *
     * @param date 对账日期
     * @return 是否成功
     */
    boolean generateReconciliation(Date date);

}
