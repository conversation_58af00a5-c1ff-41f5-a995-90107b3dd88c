/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.enums.ReconciliationCompareEnum;
import org.springblade.system.mapper.CompareBillDetailMapper;
import org.springblade.system.service.ICompareBillDetailService;
import org.springblade.system.vo.CompareBillDetailVO;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
public class ICompareBillDetailServiceImpl extends BaseServiceImpl<CompareBillDetailMapper, CompareBillDetail> implements ICompareBillDetailService {

    @Override
    public IPage<CompareBillDetailVO> selectCompareBillDetailPage(IPage<CompareBillDetailVO> page, CompareBillDetailVO compareBillDetail) {
        return page.setRecords(baseMapper.selectCompareBillDetailPage(page, compareBillDetail));
    }

}