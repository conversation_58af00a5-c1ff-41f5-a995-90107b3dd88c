package org.springblade.system.controller;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.system.dto.ManualMatchDTO;
import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.service.IChinaumsBillFileService;
import org.springblade.system.service.IDailyReconciliationService;
import org.springblade.system.service.IReconciliationExceptionService;
import org.springblade.system.task.ReconciliationTaskScheduler;
import org.springblade.system.vo.DailyReconciliationVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 对账任务控制器
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/reconciliation-task")
@Api(value = "对账任务", tags = "对账任务接口")
public class ReconciliationTaskController extends BladeController {

    private final ReconciliationTaskScheduler reconciliationTaskScheduler;
    private final IChinaumsBillFileService chinaumsBillFileService;
    private final IDailyReconciliationService dailyReconciliationService;
    private final IReconciliationExceptionService reconciliationExceptionService;

    /**
     * 手动触发对账任务
     */
    @PostMapping("/manual-reconciliation")
    @ApiOperation(value = "手动触发对账任务", notes = "手动触发指定日期的对账任务")
    public R<Boolean> manualReconciliation(
            @ApiParam(value = "对账日期，格式：yyyy-MM-dd", required = true)
            @RequestParam String date) {
        log.info("手动触发对账任务，日期：{}", date);
        
        try {
            Date reconciliationDate = DateUtil.parseDate(date);
            boolean result = reconciliationTaskScheduler.manualReconciliation(reconciliationDate);
            
            return R.data(result, result ? "对账任务执行成功" : "对账任务执行失败");
            
        } catch (Exception e) {
            log.error("手动触发对账任务失败", e);
            return R.fail("对账任务执行失败：" + e.getMessage());
        }
    }

    /**
     * 批量重试对账任务
     */
    @PostMapping("/batch-retry")
    @ApiOperation(value = "批量重试对账任务", notes = "批量重试指定时间段的对账任务")
    public R<Boolean> batchRetryReconciliation(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd", required = true)
            @RequestParam String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd", required = true)
            @RequestParam String endDate) {
        log.info("批量重试对账任务，开始日期：{}，结束日期：{}", startDate, endDate);
        
        try {
            boolean result = reconciliationTaskScheduler.batchRetryReconciliation(startDate, endDate);
            
            return R.data(result, result ? "批量重试执行成功" : "批量重试执行失败");
            
        } catch (Exception e) {
            log.error("批量重试对账任务失败", e);
            return R.fail("批量重试执行失败：" + e.getMessage());
        }
    }

    /**
     * 处理银商对账文件
     */
    @PostMapping("/process-file")
    @ApiOperation(value = "处理银商对账文件", notes = "处理指定路径的银商对账文件")
    public R<Boolean> processFile(
            @ApiParam(value = "文件路径", required = true)
            @RequestParam String filePath,
            @ApiParam(value = "对账日期，格式：yyyy-MM-dd", required = true)
            @RequestParam String date,
            @ApiParam(value = "账单类型（1-支付；2-退费）", required = true)
            @RequestParam String billType) {
        log.info("处理银商对账文件，文件路径：{}，日期：{}，类型：{}", filePath, date, billType);
        
        try {
            Date reconciliationDate = DateUtil.parseDate(date);
            boolean result = chinaumsBillFileService.processFileFromPath(filePath, reconciliationDate, billType);
            
            return R.data(result, result ? "文件处理成功" : "文件处理失败");
            
        } catch (Exception e) {
            log.error("处理银商对账文件失败", e);
            return R.fail("文件处理失败：" + e.getMessage());
        }
    }

    /**
     * 查询日对账结果
     */
    @GetMapping("/daily-results")
    @ApiOperation(value = "查询日对账结果", notes = "查询指定时间段的日对账结果")
    public R<List<DailyReconciliationVO>> queryDailyResults(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd")
            @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd")
            @RequestParam(required = false) String endDate,
            @ApiParam(value = "运营商ID")
            @RequestParam(required = false) String operatorId) {
        log.info("查询日对账结果，开始日期：{}，结束日期：{}，运营商：{}", startDate, endDate, operatorId);
        
        try {
            List<DailyReconciliationVO> results = dailyReconciliationService.queryDailyResults(startDate, endDate, operatorId);
            
            return R.data(results);
            
        } catch (Exception e) {
            log.error("查询日对账结果失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询异常对账记录
     */
    @GetMapping("/exception-records")
    @ApiOperation(value = "查询异常对账记录", notes = "查询指定条件的异常对账记录")
    public R<List<CompareBillDetail>> queryExceptionRecords(
            @ApiParam(value = "开始日期，格式：yyyy-MM-dd")
            @RequestParam(required = false) String startDate,
            @ApiParam(value = "结束日期，格式：yyyy-MM-dd")
            @RequestParam(required = false) String endDate,
            @ApiParam(value = "运营商ID")
            @RequestParam(required = false) String operatorId,
            @ApiParam(value = "交易类型（1-支付；2-退款）")
            @RequestParam(required = false) String transType) {
        log.info("查询异常对账记录，开始日期：{}，结束日期：{}，运营商：{}，交易类型：{}", 
                startDate, endDate, operatorId, transType);
        
        try {
            List<CompareBillDetail> records = reconciliationExceptionService.queryExceptionRecords(
                    startDate, endDate, operatorId, transType);
            
            return R.data(records);
            
        } catch (Exception e) {
            log.error("查询异常对账记录失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 手动匹配异常记录
     */
    @PostMapping("/manual-match")
    @ApiOperation(value = "手动匹配异常记录", notes = "手动匹配异常对账记录")
    public R<Boolean> manualMatch(@Valid @RequestBody ManualMatchDTO manualMatchDTO) {
        log.info("手动匹配异常记录，异常记录ID：{}，匹配记录ID：{}", 
                manualMatchDTO.getExceptionDetailId(), manualMatchDTO.getMatchDetailId());
        
        try {
            boolean result = reconciliationExceptionService.manualMatch(manualMatchDTO);
            
            return R.data(result, result ? "手动匹配成功" : "手动匹配失败");
            
        } catch (Exception e) {
            log.error("手动匹配异常记录失败", e);
            return R.fail("手动匹配失败：" + e.getMessage());
        }
    }

    /**
     * 强制标记为正常
     */
    @PostMapping("/force-normal")
    @ApiOperation(value = "强制标记为正常", notes = "强制将异常记录标记为正常")
    public R<Boolean> forceMarkAsNormal(
            @ApiParam(value = "对账明细ID", required = true)
            @RequestParam Long detailId,
            @ApiParam(value = "操作员ID", required = true)
            @RequestParam String operatorId,
            @ApiParam(value = "备注")
            @RequestParam(required = false) String remark) {
        log.info("强制标记为正常，记录ID：{}，操作员：{}", detailId, operatorId);
        
        try {
            boolean result = reconciliationExceptionService.forceMarkAsNormal(detailId, operatorId, remark);
            
            return R.data(result, result ? "标记成功" : "标记失败");
            
        } catch (Exception e) {
            log.error("强制标记为正常失败", e);
            return R.fail("标记失败：" + e.getMessage());
        }
    }

    /**
     * 查找可匹配的记录
     */
    @GetMapping("/matchable-records")
    @ApiOperation(value = "查找可匹配的记录", notes = "根据订单号和金额查找可匹配的记录")
    public R<List<CompareBillDetail>> findMatchableRecords(
            @ApiParam(value = "订单号", required = true)
            @RequestParam String orderNo,
            @ApiParam(value = "金额", required = true)
            @RequestParam String amount,
            @ApiParam(value = "平台（nypt-能源平台；chinaums-银商）", required = true)
            @RequestParam String platform,
            @ApiParam(value = "交易类型（1-支付；2-退款）", required = true)
            @RequestParam String transType) {
        log.info("查找可匹配记录，订单号：{}，金额：{}，平台：{}，交易类型：{}", 
                orderNo, amount, platform, transType);
        
        try {
            List<CompareBillDetail> records = reconciliationExceptionService.findMatchableRecords(
                    orderNo, amount, platform, transType);
            
            return R.data(records);
            
        } catch (Exception e) {
            log.error("查找可匹配记录失败", e);
            return R.fail("查找失败：" + e.getMessage());
        }
    }

    /**
     * 批量处理异常记录
     */
    @PostMapping("/batch-process")
    @ApiOperation(value = "批量处理异常记录", notes = "批量处理异常对账记录")
    public R<Boolean> batchProcessExceptions(
            @ApiParam(value = "记录ID列表", required = true)
            @RequestParam List<Long> detailIds,
            @ApiParam(value = "处理动作（ignore-忽略；retry-重试）", required = true)
            @RequestParam String action,
            @ApiParam(value = "操作员ID", required = true)
            @RequestParam String operatorId) {
        log.info("批量处理异常记录，记录数量：{}，处理动作：{}，操作员：{}", 
                detailIds.size(), action, operatorId);
        
        try {
            boolean result = reconciliationExceptionService.batchProcessExceptions(detailIds, action, operatorId);
            
            return R.data(result, result ? "批量处理成功" : "批量处理失败");
            
        } catch (Exception e) {
            log.error("批量处理异常记录失败", e);
            return R.fail("批量处理失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否需要重新对账
     */
    @GetMapping("/need-re-reconciliation")
    @ApiOperation(value = "检查是否需要重新对账", notes = "检查指定日期是否需要重新对账")
    public R<Boolean> needReReconciliation(
            @ApiParam(value = "检查日期，格式：yyyy-MM-dd", required = true)
            @RequestParam String date) {
        log.info("检查是否需要重新对账，日期：{}", date);
        
        try {
            Date checkDate = DateUtil.parseDate(date);
            boolean needRe = dailyReconciliationService.needReReconciliation(checkDate);
            
            return R.data(needRe, needRe ? "需要重新对账" : "无需重新对账");
            
        } catch (Exception e) {
            log.error("检查是否需要重新对账失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }
}
