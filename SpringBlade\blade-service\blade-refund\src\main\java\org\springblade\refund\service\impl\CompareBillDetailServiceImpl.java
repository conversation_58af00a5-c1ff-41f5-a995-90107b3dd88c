package org.springblade.refund.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.refund.entity.CompareBillDetail;
import org.springblade.refund.mapper.CompareBillDetailMapper;
import org.springblade.refund.service.ICompareBillDetailService;
import org.springblade.refund.vo.CompareBillDetailVO;
import org.springframework.stereotype.Service;

/**
 * 对账明细表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class CompareBillDetailServiceImpl extends ServiceImpl<CompareBillDetailMapper, CompareBillDetail> implements ICompareBillDetailService {

    @Override
    public IPage<CompareBillDetailVO> selectCompareBillDetailPage(IPage<CompareBillDetailVO> page, CompareBillDetailVO compareBillDetail) {
        return page.setRecords(baseMapper.selectCompareBillDetailPage(page, compareBillDetail));
    }

}