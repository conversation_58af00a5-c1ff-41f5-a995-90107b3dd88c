package org.springblade.system.enums;

public interface ReconciliationCompareEnum {

	/**
	 * 平账状态 0-未平账；1-已平账
	 */
	enum BALANCE_STATUS {
		UNFINISHED_ACCOUNTS("0", "未平账"),
		SETTLED_ACCOUNTS("1", "已平账");

		private String code;
		private String desc;

		BALANCE_STATUS(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public String getCode() {
			return this.code;
		}
		public String getDesc() {
			return this.desc;
		}

		public static String[] codes() {
			String[] list = new String[values().length];
			for (int i = 0; i < values().length; i++) {
				list[i] = values()[i].code;
			}
			return list;
		}
		public static String getValue(String code) {
			BALANCE_STATUS[] prints = BALANCE_STATUS.values();
			for (BALANCE_STATUS or : prints) {
				if (or.getCode().equals(code)) {
					return or.getDesc();
				}
			}
			return null;
		}
	}

	/**
	 * 交易类型 1-支付；2-退款
	 */
	enum TRANS_TYPE {
		PAY("1", "支付"),
		REFUND("2", "退款");

		private String code;
		private String desc;

		TRANS_TYPE(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public String getCode() {
			return this.code;
		}
		public String getDesc() {
			return this.desc;
		}

		public static String[] codes() {
			String[] list = new String[values().length];
			for (int i = 0; i < values().length; i++) {
				list[i] = values()[i].code;
			}
			return list;
		}
		public static String getValue(String code) {
			TRANS_TYPE[] prints = TRANS_TYPE.values();
			for (TRANS_TYPE or : prints) {
				if (or.getCode().equals(code)) {
					return or.getDesc();
				}
			}
			return null;
		}
	}

	/**
	 * 比对类型 1-银商与移动平台账单比对；2-能源平台与星云账单比对；3-能源平台与移动平台账单比对；4-能源平台与发行ETC账单比对
	 */
	enum COMPARE_TYPE {
		CHINAUMS_YDPT("1", "银商与移动平台账单比对"),
		NYPT_NEBULA("2", "能源平台与星云账单比对"),
		NYPT_YDPT("3", "能源平台与移动平台账单比对"),
		NYPT_ETC("4", "能源平台与发行ETC账单比对"),
		NYPT_CHINAUMS("5", "能源平台与银商账单比对");

		private String code;
		private String desc;

		COMPARE_TYPE(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public String getCode() {
			return this.code;
		}
		public String getDesc() {
			return this.desc;
		}

		public static String[] codes() {
			String[] list = new String[values().length];
			for (int i = 0; i < values().length; i++) {
				list[i] = values()[i].code;
			}
			return list;
		}
		public static String getValue(String code) {
			COMPARE_TYPE[] prints = COMPARE_TYPE.values();
			for (COMPARE_TYPE or : prints) {
				if (or.getCode().equals(code)) {
					return or.getDesc();
				}
			}
			return null;
		}
	}

	/**
	 * 比对结果 0-正常；1-异常
	 */
	enum COMPARE_RESULT {
		NORMAL("0", "正常"),
		UNNORMAL("1", "异常");

		private String code;
		private String desc;

		COMPARE_RESULT(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public String getCode() {
			return this.code;
		}
		public String getDesc() {
			return this.desc;
		}

		public static String[] codes() {
			String[] list = new String[values().length];
			for (int i = 0; i < values().length; i++) {
				list[i] = values()[i].code;
			}
			return list;
		}
		public static String getValue(String code) {
			COMPARE_RESULT[] prints = COMPARE_RESULT.values();
			for (COMPARE_RESULT or : prints) {
				if (or.getCode().equals(code)) {
					return or.getDesc();
				}
			}
			return null;
		}
	}

	/**
	 * 支付渠道 1-银商[微信]；2-ETC
	 */
	enum FUNDS_FROM {
		WECHAT("1", "微信"),
		CHINAUMS_WECHAT("11", "银商[微信]"),
		ETC("2", "ETC");

		private String code;
		private String desc;

		FUNDS_FROM(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public String getCode() {
			return this.code;
		}
		public String getDesc() {
			return this.desc;
		}

		public static String[] codes() {
			String[] list = new String[values().length];
			for (int i = 0; i < values().length; i++) {
				list[i] = values()[i].code;
			}
			return list;
		}
		public static String getValue(String code) {
			FUNDS_FROM[] prints = FUNDS_FROM.values();
			for (FUNDS_FROM or : prints) {
				if (or.getCode().equals(code)) {
					return or.getDesc();
				}
			}
			return null;
		}
	}

}
