<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.CompareBillResultMapper">

    <select id="selectCompareBillResultPage" resultType="org.springblade.system.vo.CompareBillResultVO">
        select * from compare_bill_result where is_deleted = 0
        <if test="compareBillResult.compareStatus != null and compareBillResult.compareStatus != ''">
            AND compare_status= #{compareBillResult.compareStatus}
        </if>
        <if test="compareBillResult.transType != null and compareBillResult.transType != ''">
            AND trans_type= #{compareBillResult.transType}
        </if>
        <if test="compareBillResult.fundsFrom != null and compareBillResult.fundsFrom != ''">
            AND funds_from= #{compareBillResult.fundsFrom}
        </if>
        <if test="compareBillResult.chargeDateStart != null and compareBillResult.chargeDateStart != ''">
            AND charge_date >=  #{compareBillResult.chargeDateStart}
        </if>
        <if test="compareBillResult.chargeDateEnd != null and compareBillResult.chargeDateEnd != ''">
            AND charge_date &lt; #{compareBillResult.chargeDateEnd}
        </if>
        ORDER BY charge_date DESC
    </select>

    <select id="selectCompareBillResultPage2" resultType="org.springblade.system.vo.CompareBillResultVO">
        select * from compare_bill_result where is_deleted = 0
        <if test="compareBillResult.operatorId != null and compareBillResult.operatorId != ''">
            and operator_id = #{compareBillResult.operatorId}
        </if>
        <if test="compareBillResult.fundsFrom != null">
            and funds_from = #{compareBillResult.fundsFrom}
        </if>
        <if test="compareBillResult.chargeDate != null and compareBillResult.chargeDate != ''">
            and charge_date = #{compareBillResult.chargeDate}
        </if>
        <if test="compareBillResult.chargeDateStart != null and compareBillResult.chargeDateStart != ''">
            and charge_date &gt;= #{compareBillResult.chargeDateStart}
        </if>
        <if test="compareBillResult.chargeDateEnd != null and compareBillResult.chargeDateEnd != ''">
            and charge_date &lt;= #{compareBillResult.chargeDateEnd}
        </if>
        <if test="compareBillResult.compareStatus != null and compareBillResult.compareStatus != ''">
            and compare_status = #{compareBillResult.compareStatus}
        </if>
        <if test="compareBillResult.transType != null and compareBillResult.transType != ''">
            and trans_type = #{compareBillResult.transType}
        </if>
    </select>

</mapper>
