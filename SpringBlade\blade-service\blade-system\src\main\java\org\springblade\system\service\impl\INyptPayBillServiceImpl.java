/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import org.springblade.system.entity.ChargeOrderInfo;
import org.springblade.system.entity.NyptPayBill;
import org.springblade.system.feign.ChargeOrderClient;
import org.springblade.system.mapper.NyptPayBillMapper;
import org.springblade.system.service.INyptPayBillService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import org.springblade.core.tool.utils.DateUtil;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
public class INyptPayBillServiceImpl extends BaseServiceImpl<NyptPayBillMapper, NyptPayBill> implements INyptPayBillService {

    @Autowired
    private ChargeOrderClient chargeOrderClient;

    @Override
    public List<NyptPayBill> listByDate(Date date) {
        return baseMapper.selectByDate(date);
    }

    @Override
    public NyptPayBill getByOrderNo(String mainOrderCode) {
        return baseMapper.selectByOrderNo(mainOrderCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateReconciliation(Date date) {
        log.info("开始生成支付对账数据，日期：{}", DateUtil.formatDate(date));

        // 获取指定日期的充电订单列表, 将Date转为yyyy-MM-dd格式字符串
        String formattedDate = DateUtil.format(date, "yyyy-MM-dd");
        R<List<ChargeOrderInfo>> orderResult = chargeOrderClient.listByDate(formattedDate);
        if (!orderResult.isSuccess() || CollectionUtil.isEmpty(orderResult.getData())) {
            log.warn("未找到指定日期的支付充电订单数据，日期：{}", DateUtil.formatDate(date));
            XxlJobLogger.log("未找到指定日期的支付充电订单数据，日期：{}", DateUtil.formatDate(date));
            return false;
        }

        List<ChargeOrderInfo> orderList = orderResult.getData();
        log.info("获取到支付充电订单数据，数量：{}", orderList.size());
        XxlJobLogger.log("获取到支付充电订单数据，数量：{}", orderList.size());

        // 获取指定日期的支付对账明细列表
        List<NyptPayBill> existingBills = this.listByDate(date);
        Map<String, NyptPayBill> billMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(existingBills)) {
            billMap = existingBills.stream().collect(Collectors.toMap(NyptPayBill::getMainOrderCode, bill -> bill, (o1, o2) -> o1));
        }

        // 生成对账数据
        List<NyptPayBill> billsToSave = new ArrayList<>();
        for (ChargeOrderInfo order : orderList) {
            // 检查是否已存在该订单的账单
            // 已存在的账单不做处理，因为对账数据是不可变的
            if (!billMap.containsKey(order.getMainOrderCode())) {
                // 创建新的账单
                NyptPayBill bill = new NyptPayBill();
                // 根据映射关系设置字段
                bill.setMainOrderCode(order.getMainOrderCode());
                bill.setBusinessOrderNo(order.getPayOrderNo());
                bill.setTransDate(order.getCompleteTime() != null ? DateUtil.format(order.getCompleteTime(), "yyyy-MM-dd HH:mm:ss") : "");
                bill.setPayAmount(order.getActualAmount() != null ? order.getActualAmount().toString() : "");

                // 设置对账日期，格式yyyyMMdd
                String versionNo = DateUtil.format(date, "yyyyMMdd");
                bill.setVersionNo(versionNo); // versionNo取对账日期

                // 设置运营商信息和发票主体信息
                bill.setOperatorId(order.getOperatorId());
                bill.setOperatorCode(order.getOperatorCode());
                bill.setInvoiceSubjectId(order.getInvoiceSubjectId());
                bill.setInvoiceSubjectCode(order.getInvoiceSubjectCode());

                // 设置支付渠道和来源
                bill.setFundsFrom(String.valueOf(order.getFundsFrom()));
                bill.setSource(String.valueOf(order.getSource()));

                billsToSave.add(bill);
            }
        }

        // 保存对账数据
        if (CollectionUtil.isNotEmpty(billsToSave)) {
            log.info("保存对账数据，数量：{}", billsToSave.size());
            XxlJobLogger.log("保存对账数据，数量：{}", billsToSave.size());
            return this.saveBatch(billsToSave);
        }

        return true;
    }
}
