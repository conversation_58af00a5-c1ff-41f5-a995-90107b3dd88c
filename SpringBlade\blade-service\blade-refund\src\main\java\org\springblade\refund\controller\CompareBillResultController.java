package org.springblade.refund.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.refund.dto.RetryCompareDTO;
import org.springblade.refund.entity.CompareBillResult;
import org.springblade.refund.service.ICompareBillDetailService;
import org.springblade.refund.service.ICompareBillResultService;
import org.springblade.refund.vo.CompareBillDetailVO;
import org.springblade.refund.vo.CompareBillResultVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 对账结果表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/compare-bill")
@Api(value = "对账结果表", tags = "对账结果表接口")
public class CompareBillResultController extends BladeController {

    private final ICompareBillResultService compareBillResultService;
    private final ICompareBillDetailService compareBillDetailService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入compareBillResult")
    public R<CompareBillResult> detail(CompareBillResult compareBillResult) {
        CompareBillResult detail = compareBillResultService.getOne(Condition.getQueryWrapper(compareBillResult));
        return R.data(detail);
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页", notes = "传入compareBillResult")
    public R<IPage<CompareBillResultVO>> page(CompareBillResultVO compareBillResult, Query query) {
        IPage<CompareBillResultVO> pages = compareBillResultService.selectCompareBillResultPage(Condition.getPage(query), compareBillResult);
        return R.data(pages);
    }

    /**
     * 重试对账
     */
    @PostMapping("/retry")
    @ApiOperation(value = "重试对账", notes = "传入重试对账参数")
    public R<Boolean> retry(@Valid @RequestBody RetryCompareDTO retryCompareDTO) {
        boolean result = compareBillResultService.retry(retryCompareDTO.getStartDate(), retryCompareDTO.getEndDate());
        return R.data(result);
    }

    /**
     * 导出对账数据
     */
    @GetMapping("/export")
    @ApiOperation(value = "导出对账数据", notes = "传入查询条件")
    public R<Map<String, String>> export(CompareBillResultVO compareBillResult) {
        String filePath = compareBillResultService.exportData(compareBillResult);
        if (Func.isEmpty(filePath)) {
            return R.fail("导出失败");
        }
        Map<String, String> result = new HashMap<>();
        result.put("filePath", filePath);
        return R.data(result);
    }

    /**
     * 分页查询（包含明细数据）
     */
    @GetMapping("/include_details_page")
    @ApiOperation(value = "分页查询（包含明细数据）", notes = "传入查询条件，一次性返回汇总和明细数据")
    public R<Map<String, Object>> includeDetailsPage(CompareBillResultVO compareBillResult, Query query,
                                                   CompareBillDetailVO detailVO) {
        // 查询汇总数据
        IPage<CompareBillResultVO> summaryPage = compareBillResultService.selectCompareBillResultPage(Condition.getPage(query), compareBillResult);
        
        // 准备明细查询条件
        if (detailVO == null) {
            detailVO = new CompareBillDetailVO();
        }
        
        // 从汇总查询条件中复制通用条件到明细查询条件
        detailVO.setOperatorId(compareBillResult.getOperatorId());
        detailVO.setFundsFrom(compareBillResult.getFundsFrom());
        detailVO.setCompareResult(compareBillResult.getCompareStatus());
        detailVO.setTransType(compareBillResult.getTransType());
        
        // 处理日期范围转换为版本号范围
        if (Func.isNotEmpty(compareBillResult.getChargeDate())) {
            detailVO.setVersionNo(compareBillResult.getChargeDate().replace("-", ""));
        }
        if (Func.isNotEmpty(compareBillResult.getChargeDateStart())) {
            detailVO.setVersionNoStart(compareBillResult.getChargeDateStart().replace("-", ""));
        }
        if (Func.isNotEmpty(compareBillResult.getChargeDateEnd())) {
            detailVO.setVersionNoEnd(compareBillResult.getChargeDateEnd().replace("-", ""));
        }
        
        // 查询明细数据
        IPage<CompareBillDetailVO> detailPage = compareBillDetailService.selectCompareBillDetailPage(Condition.getPage(query), detailVO);
        
        // 组装返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("summaryRecords", summaryPage.getRecords());
        result.put("summaryTotal", summaryPage.getTotal());
        result.put("detailRecords", detailPage.getRecords());
        result.put("detailTotal", detailPage.getTotal());
        
        return R.data(result);
    }
}