package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.system.entity.InvoiceIssuer;
import org.springblade.system.mapper.InvoiceIssuerMapper;
import org.springblade.system.service.IInvoiceIssuerService;
import org.springframework.stereotype.Service;

@Service
public class InvoiceIssuerServiceImpl extends ServiceImpl<InvoiceIssuerMapper, InvoiceIssuer> implements IInvoiceIssuerService {
}

