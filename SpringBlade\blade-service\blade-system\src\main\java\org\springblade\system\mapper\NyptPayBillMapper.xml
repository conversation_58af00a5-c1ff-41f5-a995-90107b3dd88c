<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.NyptPayBillMapper">

    <select id="selectByDate" resultType="org.springblade.system.vo.NyptPayBillVO">
        select *
        from nypt_pay_bill
        where is_deleted = 0
        and DATE(trans_date) = DATE(#{date})
    </select>

    <select id="selectByOrderNo" resultType="org.springblade.system.vo.NyptPayBillVO">
        select *
        from nypt_pay_bill
        where is_deleted = 0
        and main_order_code = #{mainOrderCode}
    </select>

</mapper>
