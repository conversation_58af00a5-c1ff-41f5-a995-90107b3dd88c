package org.springblade.refund.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.refund.entity.CompareBillDetail;
import org.springblade.refund.entity.CompareBillResult;
import org.springblade.refund.mapper.CompareBillResultMapper;
import org.springblade.refund.service.ICompareBillDetailService;
import org.springblade.refund.service.ICompareBillResultService;
import org.springblade.refund.vo.CompareBillDetailVO;
import org.springblade.refund.vo.CompareBillResultVO;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 对账结果表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class CompareBillResultServiceImpl extends ServiceImpl<CompareBillResultMapper, CompareBillResult> implements ICompareBillResultService {

    private final ICompareBillDetailService compareBillDetailService;

    @Override
    public IPage<CompareBillResultVO> selectCompareBillResultPage(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult) {
        return page.setRecords(baseMapper.selectCompareBillResultPage(page, compareBillResult));
    }

    @Override
    public boolean retry(RetryCompareDTO retryCompareDTO) {
        String startDateStr = retryCompareDTO.getStartDate();
        String endDateStr = retryCompareDTO.getEndDate();

        // 1. 解析日期
        LocalDate startDate;
        LocalDate endDate;
        try {
            startDate = LocalDate.parse(startDateStr);
            endDate = LocalDate.parse(endDateStr);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd", e);
        }

        // 2. 检查日期范围是否合理
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        // 获取当前日期作为基准（只计算一次）
        LocalDate today = LocalDate.now();
        boolean allSuccess = true;
        LocalDate currentDate = startDate;

        // 3. 循环处理每一天
        while (!currentDate.isAfter(endDate)) {
            // 如果当前日期是今天或将来，则跳出循环（因为只处理过去的）
            if (!currentDate.isBefore(today)) {
                break;
            }

            // 计算与基准日期的天数差（为负数，因为currentDate是过去）
            long daysBetween = ChronoUnit.DAYS.between(today, currentDate);
            int dateInt = (int) Math.abs(daysBetween); // 取绝对值

            XxlJobLogger.log("===========定时生成能源平台与银商账单比对结果【开始执行】===========");
            log.info("===========定时生成能源平台与银商账单比对结果【开始执行】===========");
            try {

                // 1. 生成能源系统前一天的支付和退费对账数据
                generateNyptBillData(dateInt);

                // 2. 处理银商系统提供的对账文件
                processChinaumsBillFile(dateInt);

                // 3. 执行对账比对
                // 能源平台与银商支付平台支付账单比对
                XxlJobLogger.log("===========能源平台与银商账单比对【开始执行】===========");
                log.info("===========能源平台与银商账单比对【开始执行】===========");

                nyptAndChinaumsPayBillCompare(dateInt);
                // 能源平台与银商支付平台退费账单比对
                nyptAndChinaumsRefundBillCompare(dateInt);
                XxlJobLogger.log("===========能源平台与银商账单比对【执行成功】===========");
                log.info("===========能源平台与银商账单比对【执行成功】===========");
                log.info("===========定时生成能源平台与银商账单比对结果【执行成功】===========");
                XxlJobLogger.log("===========定时生成能源平台与银商账单比对结果【执行成功】===========");
            } catch (Exception e) {
                log.error("===========定时生成能源平台与银商账单比对结果【执行异常】===========：{}", e.getMessage(), e);
                XxlJobLogger.log("===========定时生成能源平台与银商账单比对结果【执行异常】===========：" + e);
                allSuccess = false;
            }

            currentDate = currentDate.plusDays(1); // 处理下一天
        }

        return allSuccess;
    }

    @Override
    public String exportData(CompareBillResultVO compareBillResult) {
        // 查询汇总数据
        List<CompareBillResult> resultList = list(new LambdaQueryWrapper<CompareBillResult>()
            .eq(compareBillResult.getOperatorId() != null, CompareBillResult::getOperatorId, compareBillResult.getOperatorId())
            .eq(compareBillResult.getFundsFrom() != null, CompareBillResult::getFundsFrom, compareBillResult.getFundsFrom())
            .eq(StringUtil.isNotBlank(compareBillResult.getChargeDate()), CompareBillResult::getChargeDate, compareBillResult.getChargeDate())
            .ge(StringUtil.isNotBlank(compareBillResult.getChargeDateStart()), CompareBillResult::getChargeDate, compareBillResult.getChargeDateStart())
            .le(StringUtil.isNotBlank(compareBillResult.getChargeDateEnd()), CompareBillResult::getChargeDate, compareBillResult.getChargeDateEnd())
            .eq(StringUtil.isNotBlank(compareBillResult.getCompareStatus()), CompareBillResult::getCompareStatus, compareBillResult.getCompareStatus())
            .eq(StringUtil.isNotBlank(compareBillResult.getTransType()), CompareBillResult::getTransType, compareBillResult.getTransType())
            .orderByDesc(CompareBillResult::getCreateTime));

        // 查询明细数据
        CompareBillDetailVO detailVO = new CompareBillDetailVO();
        detailVO.setOperatorId(compareBillResult.getOperatorId());
        detailVO.setFundsFrom(compareBillResult.getFundsFrom());
        if (StringUtil.isNotBlank(compareBillResult.getChargeDate())) {
            detailVO.setVersionNo(compareBillResult.getChargeDate().replace("-", ""));
        }
        if (StringUtil.isNotBlank(compareBillResult.getChargeDateStart())) {
            detailVO.setVersionNoStart(compareBillResult.getChargeDateStart().replace("-", ""));
        }
        if (StringUtil.isNotBlank(compareBillResult.getChargeDateEnd())) {
            detailVO.setVersionNoEnd(compareBillResult.getChargeDateEnd().replace("-", ""));
        }

        List<CompareBillDetail> detailList = compareBillDetailService.list(new LambdaQueryWrapper<CompareBillDetail>()
            .eq(detailVO.getOperatorId() != null, CompareBillDetail::getOperatorId, detailVO.getOperatorId())
            .eq(detailVO.getFundsFrom() != null, CompareBillDetail::getFundsFrom, detailVO.getFundsFrom())
            .eq(StringUtil.isNotBlank(detailVO.getVersionNo()), CompareBillDetail::getVersionNo, detailVO.getVersionNo())
            .ge(StringUtil.isNotBlank(detailVO.getVersionNoStart()), CompareBillDetail::getVersionNo, detailVO.getVersionNoStart())
            .le(StringUtil.isNotBlank(detailVO.getVersionNoEnd()), CompareBillDetail::getVersionNo, detailVO.getVersionNoEnd())
            .eq(StringUtil.isNotBlank(compareBillResult.getCompareStatus()), CompareBillDetail::getCompareResult, compareBillResult.getCompareStatus())
            .eq(StringUtil.isNotBlank(compareBillResult.getTransType()), CompareBillDetail::getTransType, compareBillResult.getTransType())
            .orderByDesc(CompareBillDetail::getCreateTime));

        // 生成Excel文件
        String fileName = "对账数据_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;

        try (ExcelWriter excelWriter = EasyExcel.write(filePath).build()) {
            // 写入汇总数据
            WriteSheet summarySheet = EasyExcel.writerSheet(0, "对账汇总").head(CompareBillResult.class).build();
            excelWriter.write(resultList, summarySheet);

            // 写入明细数据
            WriteSheet detailSheet = EasyExcel.writerSheet(1, "对账明细").head(CompareBillDetail.class).build();
            excelWriter.write(detailList, detailSheet);
        } catch (Exception e) {
            log.error("导出对账数据失败", e);
            return null;
        }

        return filePath;
    }
}