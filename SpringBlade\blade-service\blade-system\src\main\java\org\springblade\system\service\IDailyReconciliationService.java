package org.springblade.system.service;

import org.springblade.system.entity.CompareBillResult;
import org.springblade.system.vo.DailyReconciliationVO;

import java.util.Date;
import java.util.List;

/**
 * 日对账结果统计服务接口
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IDailyReconciliationService {

    /**
     * 生成日对账结果统计
     * @param date 对账日期
     * @return 生成结果
     */
    boolean generateDailyResult(Date date);

    /**
     * 查询日对账结果
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param operatorId 运营商ID
     * @return 日对账结果列表
     */
    List<DailyReconciliationVO> queryDailyResults(String startDate, String endDate, String operatorId);

    /**
     * 检查指定日期是否需要重新对账
     * @param date 检查日期
     * @return 是否需要重新对账
     */
    boolean needReReconciliation(Date date);

    /**
     * 获取对账执行状态
     * @param date 对账日期
     * @param operatorId 运营商ID
     * @return 执行状态
     */
    String getReconciliationStatus(Date date, String operatorId);

    /**
     * 更新对账执行状态
     * @param date 对账日期
     * @param operatorId 运营商ID
     * @param status 状态
     * @return 更新结果
     */
    boolean updateReconciliationStatus(Date date, String operatorId, String status);
}
