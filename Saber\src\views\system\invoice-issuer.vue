<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="data"
      :table-loading="loading"
      v-model="form"
      ref="crud"
      @on-load="onLoad"
      @current-change="currentChange"
      @size-change="sizeChange"
    >
      <template #menu="scope">
        <el-button text type="primary" @click.stop="openAssociate(scope.row)">关联机构</el-button>
      </template>
    </avue-crud>

    <el-dialog title="关联机构" v-model="dialogVisible" width="600px" append-to-body>
      <avue-tree :option="treeOption" :data="treeData" ref="orgTree" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible=false">取 消</el-button>
          <el-button type="primary" @click="saveAssociate">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList as getInvoiceIssuerList, associateOrgs, getOrgIds } from '@/api/system/invoice-issuer'
import { getDeptLazyTree } from '@/api/system/dept'
export default {
  data () {
    return {
      data: [],
      loading: false,
      form: {},
      page: { currentPage: 1, pageSize: 10, total: 0 },
      option: {
        border: true,
        index: true,
        menuWidth: 200,
        column: [
          { label: '销售ID', prop: 'salesId' },
          { label: '销售编码', prop: 'salesCode' }
        ]
      },
      dialogVisible: false,
      currentSalesId: '',
      treeData: [],
      treeOption: {
        nodeKey: 'id',
        lazy: true,
        checkStrictly: true,
        multiple: true,
        defaultExpandAll: false,
        defaultExpandKeys: [],
        showCheckbox: true,
        addBtn: false,
        menu: false,
        treeLoad: (node, resolve) => {
          const parentId = (node.level === 0) ? 0 : node.data.id
          getDeptLazyTree(parentId).then(res => {
            const list = res.data.data.map(item => ({
              ...item,
              leaf: !item.hasChildren
            }))
            resolve(list)
          })
        },
        props: {
          labelText: '部门',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      }
    }
  },
  methods: {
    onLoad (page, params = {}) {
      this.loading = true
      getInvoiceIssuerList(page.currentPage, page.pageSize, params).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records || data
        this.loading = false
      })
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
      this.onLoad(this.page)
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
      this.onLoad(this.page)
    },
    openAssociate (row) {
      this.currentSalesId = row.salesId
      this.dialogVisible = true

      // 重置树数据，避免重复显示
      this.$nextTick(() => {
        // 清空之前的数据
        this.treeData = []

        // 初始化根节点
        getDeptLazyTree(0).then(res => {
          this.treeData = res.data.data.map(item => ({
            ...item,
            leaf: !item.hasChildren
          }))

          // 加载已选中节点 - 仅设置实际保存的节点，不级联选择
          getOrgIds(this.currentSalesId).then(r => {
            const ids = r.data.data || []
            this.$nextTick(() => {
              // 使用 checkStrictly: true 模式，仅选中保存的节点，不自动选择子节点
              this.$refs.orgTree.setCheckedKeys(ids, false)
            })
          })
        })
      })
    },
    saveAssociate () {
      // 使用 checkStrictly: true 和 multiple: true 模式，仅保存用户实际选中的节点
      const checkedKeys = this.$refs.orgTree.getCheckedKeys()

      associateOrgs(this.currentSalesId, checkedKeys).then(() => {
        this.$message.success('保存成功')
        this.dialogVisible = false
      })
    }
  }
}
</script>

