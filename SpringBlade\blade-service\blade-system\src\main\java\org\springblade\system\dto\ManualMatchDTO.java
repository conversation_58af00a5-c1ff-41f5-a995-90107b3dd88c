package org.springblade.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 手动匹配DTO
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@ApiModel(value = "ManualMatchDTO", description = "手动匹配参数")
public class ManualMatchDTO {

    @NotNull
    @ApiModelProperty(value = "异常记录ID", required = true)
    private Long exceptionDetailId;

    @NotNull
    @ApiModelProperty(value = "匹配记录ID", required = true)
    private Long matchDetailId;

    @ApiModelProperty(value = "操作员ID")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    private String operatorName;

    @ApiModelProperty(value = "匹配备注")
    private String remark;

    @ApiModelProperty(value = "是否强制匹配（忽略金额差异）")
    private Boolean forceMatch = false;
}
