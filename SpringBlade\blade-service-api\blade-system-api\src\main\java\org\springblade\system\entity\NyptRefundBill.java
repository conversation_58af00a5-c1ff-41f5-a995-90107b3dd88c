package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 能源平台退款账单实体
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("nypt_refund_bill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NyptRefundBill对象", description = "能源平台退款账单表")
public class NyptRefundBill extends BaseEntity {

    /**
     * 充电主订单号
     */
    @ApiModelProperty(value = "充电主订单号")
    private String mainOrderCode;
    /**
     * 充电退费ID
     */
    @ApiModelProperty(value = "充电退费ID")
    private String refundId;
    /**
     * 三方支付订单号
     */
    @ApiModelProperty(value = "三方支付订单号")
    private String payOrderNo;
    /**
     * 退款时间
     */
    @ApiModelProperty(value = "退款时间")
    private String refundDate;
    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;
    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;
    /**
     * 支付来源(1-正常退款；2-客诉退款；3-充电失败退款；4-退款修复)
     */
    @ApiModelProperty(value = "支付来源")
    private int source;
    /**
     * 退款类型(1-原路退回)
     */
    @ApiModelProperty(value = "退款类型")
    private int type;
    /**
     * 退费人标识(暂定：CDZXCX)
     */
    @ApiModelProperty(value = "退费人标识")
    private String userId;
    /**
     * 退费人名称(暂定：充电桩小程序)
     */
    @ApiModelProperty(value = "退费人名称")
    private String userName;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionNo;
    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;

}