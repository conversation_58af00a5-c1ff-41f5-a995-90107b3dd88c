package org.springblade.system.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.InvoiceIssuer;
import org.springblade.system.service.IInvoiceIssuerService;
import org.springblade.system.service.IOrgInvoiceIssuerMappingService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 开票方 控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping("/invoice-issuer")
@Tag(name = "开票方管理", description = "开票方管理接口")
public class InvoiceIssuerController extends BladeController {

    private final IInvoiceIssuerService invoiceIssuerService;
    private final IOrgInvoiceIssuerMappingService mappingService;

    /**
     * 列表（分页）
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "列表", description = "传入查询参数")
    public R<?> list(@RequestParam Map<String, Object> params, Query query) {
        return R.data(invoiceIssuerService.page(Condition.getPage(query), Condition.getQueryWrapper(params, InvoiceIssuer.class)));
    }

    /**
     * 查询已关联的机构ID列表
     */
    @GetMapping("/org-ids")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "查询已关联机构", description = "根据salesId查询机构ID列表")
    public R<List<Long>> getOrgIds(@Parameter(description = "开票方salesId", required = true, in = ParameterIn.QUERY) @RequestParam String salesId) {
        return R.data(mappingService.getOrgIdsBySalesId(salesId));
    }

    /**
     * 保存机构关联
     */
    @PostMapping("/associate-orgs")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "保存机构关联", description = "为开票方批量设置关联机构")
    public R<Boolean> associateOrgs(@RequestBody Map<String, Object> body) {
        String salesId = Func.toStr(body.get("salesId"));
        List<Long> orgIds = Func.toLongList(body.get("orgIds"));
        // 校验开票方是否存在
        boolean exists = invoiceIssuerService.lambdaQuery().eq(InvoiceIssuer::getSalesId, salesId).count() > 0;
        if (!exists) {
            return R.fail("开票方不存在: " + salesId);
        }
        boolean ok = mappingService.saveMappings(salesId, orgIds);
        return R.status(ok);
    }
}

