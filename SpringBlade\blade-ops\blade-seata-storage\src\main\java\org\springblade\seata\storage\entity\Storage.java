package org.springblade.seata.storage.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * storage
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_storage")
public class Storage implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private Long id;
	private String commodityCode;
	private Long count;

}
