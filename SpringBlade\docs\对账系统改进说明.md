# 对账系统改进说明

## 改进概述

根据重新梳理的对账需求，对现有的 `nyptAndChinaumsPayBillCompare` 和 `nyptAndChinaumsRefundBillCompare` 方法进行了完善，特别针对退费时多笔相同订单号的情况，优化了对账逻辑以提高匹配准确性。

## 主要改进内容

### 1. 退费对账逻辑优化 (`processRefundReconciliation`)

#### 问题分析
- **原有问题**：直接使用 `refundId` 进行一对一匹配，无法处理同一订单多笔退费的情况
- **业务需求**：银商系统返回的退费订单号与支付订单号相同，同一订单的多笔退费使用相同的退费订单号

#### 改进方案
```java
// 按主订单号分组能源平台退费记录
Map<String, List<NyptRefundBill>> nyptBillsByMainOrder = nyptBills.stream()
        .collect(Collectors.groupingBy(NyptRefundBill::getMainOrderCode));

// 按退费ID（实际是支付订单号）分组银商退费记录
Map<String, List<ChinaumsRefundBill>> chinaumsBillsByRefundId = chinaumsBills.stream()
        .collect(Collectors.groupingBy(ChinaumsRefundBill::getRefundId));
```

#### 核心特性
1. **智能分组匹配**：按主订单号和支付订单号进行分组匹配
2. **时间优先匹配**：在多笔退费中选择时间最接近的记录进行匹配
3. **金额优先原则**：优先匹配金额相同的记录，其次考虑时间接近度

### 2. 支付对账逻辑优化 (`processPayReconciliation`)

#### 改进内容
- 虽然支付订单号通常是唯一的，但为提高对账准确性，也加入了时间验证逻辑
- 支持处理可能存在的重复支付订单号情况

#### 匹配策略
```java
// 支持多个相同订单号的情况
Map<String, List<ChinaumsPayBill>> chinaumsBillMap = chinaumsBills.stream()
        .collect(Collectors.groupingBy(ChinaumsPayBill::getBusinessOrderNo));
```

### 3. 时间匹配算法

#### 最佳匹配策略
1. **第一优先级**：金额完全匹配的记录
2. **第二优先级**：在金额匹配的记录中选择时间最接近的
3. **第三优先级**：如果没有金额匹配，选择时间最接近的记录

#### 时间解析支持
```java
// 支持多种日期格式
String[] patterns = {
    "yyyy-MM-dd HH:mm:ss",
    "yyyy-MM-dd",
    "yyyy/MM/dd HH:mm:ss",
    "yyyy/MM/dd",
    "yyyyMMdd HH:mm:ss",
    "yyyyMMdd"
};
```

### 4. 新增核心方法

#### `handleRefundBothExistWithTimeMatching`
- 处理双方都存在退费记录的情况
- 使用时间匹配优化对账准确性
- 支持一对多的智能匹配

#### `findBestRefundMatch`
- 为能源平台退费记录找到最佳匹配的银商退费记录
- 综合考虑金额和时间因素

#### `findClosestTimeMatch`
- 从银商记录列表中找到时间最接近的记录
- 支持退费和支付两种场景

#### `parseRefundDate`
- 解析多种日期格式
- 提供统一的日期处理能力

## 业务场景支持

### 场景1：正常退费
- 一笔支付对应一笔退费
- 直接按订单号和金额匹配

### 场景2：多笔退费
- 一笔支付对应多笔退费（如部分退费、客诉退款等）
- 按时间匹配最合适的退费记录

### 场景3：时间差异
- 能源系统和银商系统的交易时间可能存在差异
- 通过时间接近度算法找到最佳匹配

### 场景4：异常处理
- 金额不匹配的记录标记为异常
- 支持后续手动处理和强制匹配

## 对账准确性提升

### 1. 匹配准确率
- **金额匹配**：确保金额一致性
- **时间匹配**：在多个候选记录中选择最合理的匹配
- **订单关联**：通过主订单号建立正确的业务关联

### 2. 异常识别
- 更精确地识别真正的异常记录
- 减少因匹配逻辑不当导致的误报

### 3. 处理效率
- 批量处理同一订单的多笔退费
- 减少重复查询和计算

## 配置和使用

### 1. 时间容差配置
可以通过配置文件设置时间匹配的容差范围：
```yaml
reconciliation:
  time-tolerance: 3600000  # 1小时，单位毫秒
```

### 2. 匹配策略配置
```yaml
reconciliation:
  match-strategy:
    amount-first: true      # 金额优先匹配
    time-tolerance: 3600000 # 时间容差
    strict-mode: false      # 严格模式
```

## 测试建议

### 1. 单元测试
- 测试各种日期格式的解析
- 测试时间匹配算法的准确性
- 测试多笔退费的匹配逻辑

### 2. 集成测试
- 测试完整的对账流程
- 测试异常情况的处理
- 测试性能和稳定性

### 3. 业务测试
- 使用真实数据进行对账测试
- 验证各种业务场景的处理结果
- 确认异常记录的准确性

## 注意事项

1. **数据质量**：确保输入数据的时间格式规范
2. **性能考虑**：大量数据时注意内存使用和处理时间
3. **异常处理**：完善的异常处理和日志记录
4. **向后兼容**：保持与现有系统的兼容性

## 后续优化方向

1. **机器学习**：引入机器学习算法优化匹配准确性
2. **实时对账**：支持实时或准实时的对账处理
3. **可视化**：提供对账结果的可视化展示
4. **自动修复**：对于常见的异常情况提供自动修复建议
