export default {
  title: 'Avue is a framework',
  tip: 'tip',
  logoutTip: 'Exit the system, do you want to continue?',
  submitText: 'submit',
  cancelText: 'cancel',
  search: 'Please input search content',
  menuTip: 'none menu list',
  feedback: 'feedback',
  submit: 'submit',
  wel: {
    info: 'Good morning, Smallwei, Avue is a framework',
    dept: 'a certain technology department',
    team: 'Team ranking',
    project: 'Project access',
    count: 'Item number',
    data: {
      subtitle: 'real time',
      column1: 'Classified statistics',
      column2: 'Annex statistics',
      column3: 'Article statistics',
      key1: 'C',
      key2: 'A',
      key3: 'A',
      text1: 'Total Record Number of Classifications',
      text2: 'Number of attachments Uploaded',
      text3: 'Comment frequency'
    },
    data2: {
      column1: 'Registration today',
      column2: 'Login today',
      column3: 'Subscription today',
      column4: 'Todays review'
    },
    data3: {
      column1: 'Conversion rate（Day 28%）',
      column2: 'Attendance rate（Day 11%）',
      column3: 'Attendance rate（Day 33%）'
    },
    data4: {
      column1: 'Error log',
      column2: 'Data display',
      column3: 'Privilege management',
      column4: 'user management'
    },
    table: {
      rw: 'Work Tasks',
      nr: 'Work content',
      sj: 'Working hours',
    }
  },
  route: {
    setting: 'setting',
    detail: 'detail',
    info: 'info',
    website: 'website',
    dashboard: 'dashboard',
    more: 'more',
    tags: 'tags',
    store: 'store',
    permission: 'permission',
    api: 'api',
    logs: 'logs',
    table: 'table',
    crud: 'crud',
    params: 'params',
    form: 'form',
    top: 'backtop',
    affix: 'affix',
    data: 'data',
    cache: 'cache',
    error: 'error',
    test: 'test',
    out: 'out',
    about: 'about'
  },
  login: {
    title: 'Login ',
    info: 'Rapid Development Framework of General Management System',
    username: 'Please input username',
    password: 'Please input a password',
    wechat: 'Wechat',
    qq: 'QQ',
    phone: 'Please input a phone',
    code: 'Please input a code',
    submit: 'Login',
    userLogin: 'userLogin',
    phoneLogin: 'phoneLogin',
    thirdLogin: 'thirdLogin',
    faceLogin: 'faceLogin',
    msgText: 'send code',
    msgSuccess: 'reissued code',
  },
  navbar: {
    setting: 'setting',
    info: 'info',
    logOut: 'logout',
    userinfo: 'userinfo',
    dashboard: 'dashboard',
    lock: 'lock',
    bug: 'none bug',
    bugs: 'bug',
    screenfullF: 'exit screenfull',
    screenfull: 'screenfull',
    language: 'language',
    notice: 'notice',
    theme: 'theme',
    color: 'color'
  },
  tagsView: {
    search: 'Search',
    menu: 'menu',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  }
}