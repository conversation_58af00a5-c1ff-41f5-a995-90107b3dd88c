# 对账系统唯一索引改进说明

## 改进背景

由于一个订单交易可能有多笔记录，使用相同的订单号，导致对账时出现重复或匹配错误的问题。为了解决这个问题，我们引入了银商检索号+银商流水号作为唯一索引，并完善了去重逻辑。

## 主要改进内容

### 1. 数据库表结构更新

#### 1.1 银商支付账单表 (`chinaums_pay_bill`)
```sql
ALTER TABLE `chinaums_pay_bill` 
ADD COLUMN `search_no` varchar(45) NOT NULL COMMENT '唯一索引，值为银商检索号+银商流水号';
```

#### 1.2 银商退费账单表 (`chinaums_refund_bill`)
```sql
ALTER TABLE `chinaums_refund_bill` 
ADD COLUMN `search_no` varchar(45) NOT NULL COMMENT '唯一索引，值为银商检索号+银商流水号';
```

#### 1.3 对账明细表 (`compare_bill_detail`)
```sql
ALTER TABLE `compare_bill_detail` 
ADD COLUMN `chinaums_search_no` varchar(45) DEFAULT NULL COMMENT '银商检索号+银商流水号（唯一索引）',
ADD COLUMN `nypt_refund_id` varchar(255) DEFAULT NULL COMMENT '能源平台退费ID';
```

### 2. 唯一索引约束

#### 2.1 银商系统唯一索引
- **组合字段**：`chinaums_business_order_no` + `chinaums_search_no`
- **作用**：确保相同订单号的不同交易记录通过检索号+流水号区分

#### 2.2 能源平台唯一索引
- **组合字段**：`nypt_business_order_no` + `nypt_refund_id`
- **作用**：确保能源平台的交易记录唯一性

### 3. 文件格式更新

#### 3.1 银商支付对账文件格式
```
主订单号|支付订单号|交易时间|支付金额|运营商ID|银商检索号|银商流水号
```

#### 3.2 银商退费对账文件格式
```
主订单号|退费ID|退款时间|退款金额|运营商ID|银商检索号|银商流水号
```

### 4. 去重逻辑实现

#### 4.1 去重检查服务 (`ReconciliationDuplicateCheckService`)
- **银商唯一索引检查**：根据订单号+检索号查找已存在记录
- **能源平台唯一索引检查**：根据订单号+退费ID查找已存在记录
- **记录更新逻辑**：更新已存在记录而不是插入新记录

#### 4.2 保存逻辑优化 (`saveOrUpdateCompareBillDetail`)
```java
private CompareBillDetail saveOrUpdateCompareBillDetail(CompareBillDetail detail) {
    // 1. 检查银商唯一索引
    CompareBillDetail existingByChinaums = duplicateCheckService.findExistingDetailByChinaums(...);
    if (existingByChinaums != null) {
        return duplicateCheckService.updateExistingDetail(existingByChinaums, detail);
    }
    
    // 2. 检查能源平台唯一索引
    CompareBillDetail existingByNypt = duplicateCheckService.findExistingDetailByNypt(...);
    if (existingByNypt != null) {
        return duplicateCheckService.updateExistingDetail(existingByNypt, detail);
    }
    
    // 3. 没有重复记录，直接保存
    compareBillDetailService.save(detail);
    return detail;
}
```

## 业务场景支持

### 场景1：同一订单多笔支付
- **问题**：相同订单号的多笔支付记录
- **解决方案**：通过银商检索号+流水号区分不同的支付记录
- **示例**：
  ```
  订单号：ORDER001，检索号+流水号：SEARCH001FLOW001
  订单号：ORDER001，检索号+流水号：SEARCH001FLOW002
  ```

### 场景2：同一订单多笔退费
- **问题**：一笔支付对应多笔退费（部分退费、客诉退款等）
- **解决方案**：
  - 银商系统：使用相同的退费订单号（支付订单号），通过检索号+流水号区分
  - 能源平台：使用不同的退费ID区分
- **示例**：
  ```
  银商退费1：退费ID=PAY001，检索号+流水号=SEARCH002FLOW001
  银商退费2：退费ID=PAY001，检索号+流水号=SEARCH002FLOW002
  能源退费1：订单号=PAY001，退费ID=REFUND001
  能源退费2：订单号=PAY001，退费ID=REFUND002
  ```

### 场景3：重复导入处理
- **问题**：银商对账文件重复导入导致数据重复
- **解决方案**：通过唯一索引检查，更新已存在记录而不是插入新记录

## 实施步骤

### 1. 数据库更新
```bash
# 执行数据库更新脚本
mysql -u username -p database_name < docs/数据库更新脚本.sql
```

### 2. 代码部署
- 部署更新后的实体类
- 部署新的对账逻辑
- 部署去重检查服务

### 3. 数据迁移（可选）
如果现有数据需要迁移，执行以下步骤：
```sql
-- 为现有记录生成临时search_no
UPDATE chinaums_pay_bill 
SET search_no = CONCAT('TEMP_', id, '_', SUBSTRING(business_order_no, -8))
WHERE search_no IS NULL OR search_no = '';

-- 为现有对账明细设置nypt_refund_id
UPDATE compare_bill_detail 
SET nypt_refund_id = nypt_business_order_no
WHERE trans_type = '1' AND (nypt_refund_id IS NULL OR nypt_refund_id = '');
```

### 4. 验证测试
- 运行单元测试验证去重逻辑
- 使用真实数据测试对账流程
- 验证唯一索引约束的有效性

## 配置说明

### 1. 文件解析配置
```yaml
reconciliation:
  chinaums:
    file:
      path: /data/reconciliation/chinaums/
      encoding: UTF-8
      format:
        pay: "主订单号|支付订单号|交易时间|支付金额|运营商ID|银商检索号|银商流水号"
        refund: "主订单号|退费ID|退款时间|退款金额|运营商ID|银商检索号|银商流水号"
```

### 2. 去重策略配置
```yaml
reconciliation:
  duplicate-check:
    enabled: true
    update-existing: true
    log-duplicates: true
```

## 监控和日志

### 1. 关键日志
- 重复记录发现和处理日志
- 唯一索引冲突日志
- 数据更新操作日志

### 2. 监控指标
- 重复记录处理数量
- 唯一索引冲突次数
- 对账成功率

## 注意事项

### 1. 数据完整性
- 确保银商检索号+流水号的完整性
- 验证能源平台退费ID的唯一性
- 定期检查数据一致性

### 2. 性能考虑
- 唯一索引会影响插入性能
- 大量重复检查可能影响处理速度
- 建议在低峰期执行数据迁移

### 3. 兼容性
- 保持与现有系统的兼容性
- 支持渐进式数据迁移
- 提供回滚方案

## 故障排除

### 1. 唯一索引冲突
```sql
-- 查找重复记录
SELECT business_order_no, search_no, COUNT(*) 
FROM chinaums_pay_bill 
GROUP BY business_order_no, search_no 
HAVING COUNT(*) > 1;
```

### 2. 空值检查
```sql
-- 检查空的search_no
SELECT COUNT(*) FROM chinaums_pay_bill 
WHERE search_no IS NULL OR search_no = '';
```

### 3. 数据修复
```sql
-- 修复空的nypt_refund_id
UPDATE compare_bill_detail 
SET nypt_refund_id = COALESCE(nypt_business_order_no, main_order_code)
WHERE nypt_refund_id IS NULL OR nypt_refund_id = '';
```

## 后续优化建议

1. **性能优化**：优化查询语句，添加必要的索引
2. **监控完善**：添加更详细的监控和告警
3. **自动化测试**：完善自动化测试用例
4. **文档更新**：持续更新操作手册和故障排除指南
