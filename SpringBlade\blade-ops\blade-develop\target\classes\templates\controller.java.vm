/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package $!{package.Controller};

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
#if($!{superEntityClass})
import org.springframework.web.bind.annotation.RequestParam;
#end
import com.baomidou.mybatisplus.core.metadata.IPage;
import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("entity","vo"))
import $!{voPackage}.$!{entity}VO;
#set($wrapperPackage=$package.Entity.replace("entity","wrapper"))
#if($!{hasWrapper})
import $!{wrapperPackage}.$!{entity}Wrapper;
#end
import $!{package.Service}.$!{table.serviceName};
#if($!{superControllerClassPackage})
import $!{superControllerClassPackage};
#end
#if(!$!{superEntityClass})
#end

/**
 * $!{table.comment} 控制器
 *
 * <AUTHOR>
 * @since $!{date}
 */
@RestController
@AllArgsConstructor
@RequestMapping("#if($!{package.ModuleName})$!{package.ModuleName}#end/$!{entityKey}")
@Tag(name = "$!{table.comment}", description = "$!{table.comment}接口")
#if($!{superControllerClass})
public class $!{table.controllerName} extends $!{superControllerClass} {
#else
public class $!{table.controllerName} {
#end

	private $!{table.serviceName} $!{table.entityPath}Service;

#if($!{hasWrapper})
	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入$!{table.entityPath}")
	public R<$!{entity}VO> detail($!{entity} $!{table.entityPath}) {
		$!{entity} detail = $!{table.entityPath}Service.getOne(Condition.getQueryWrapper($!{table.entityPath}));
		return R.data($!{entity}Wrapper.build().entityVO(detail));
	}

	/**
	 * 分页 $!{table.comment}
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}VO>> list($!{entity} $!{table.entityPath}, Query query) {
		IPage<$!{entity}> pages = $!{table.entityPath}Service.page(Condition.getPage(query), Condition.getQueryWrapper($!{table.entityPath}));
		return R.data($!{entity}Wrapper.build().pageVO(pages));
	}

#else
	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入$!{table.entityPath}")
	public R<$!{entity}> detail($!{entity} $!{table.entityPath}) {
		$!{entity} detail = $!{table.entityPath}Service.getOne(Condition.getQueryWrapper($!{table.entityPath}));
		return R.data(detail);
	}

	/**
	 * 分页 $!{table.comment}
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}>> list($!{entity} $!{table.entityPath}, Query query) {
		IPage<$!{entity}> pages = $!{table.entityPath}Service.page(Condition.getPage(query), Condition.getQueryWrapper($!{table.entityPath}));
		return R.data(pages);
	}
#end

	/**
	 * 自定义分页 $!{table.comment}
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}VO>> page($!{entity}VO $!{table.entityPath}, Query query) {
		IPage<$!{entity}VO> pages = $!{table.entityPath}Service.select$!{entity}Page(Condition.getPage(query), $!{table.entityPath});
		return R.data(pages);
	}

	/**
	 * 新增 $!{table.comment}
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入$!{table.entityPath}")
	public R save(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.save($!{table.entityPath}));
	}

	/**
	 * 修改 $!{table.comment}
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入$!{table.entityPath}")
	public R update(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.updateById($!{table.entityPath}));
	}

	/**
	 * 新增或修改 $!{table.comment}
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入$!{table.entityPath}")
	public R submit(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.saveOrUpdate($!{table.entityPath}));
	}

	#if($!{superEntityClass})

	/**
	 * 删除 $!{table.comment}
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status($!{table.entityPath}Service.deleteLogic(Func.toLongList(ids)));
	}

	#else

	/**
	 * 删除 $!{table.comment}
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status($!{table.entityPath}Service.removeByIds(Func.toLongList(ids)));
	}

	#end

}
