package org.springblade.system.vo;

import org.springblade.system.entity.CompareBillResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "CompareBillResultVO对象", description = "账单比对结果参数")
public class CompareBillResultVO extends CompareBillResult {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "条件查询 对账开始时间")
    private String chargeDateStart;

    @ApiModelProperty(value = "条件查询 对账结束时间")
    private String chargeDateEnd;

    @ApiModelProperty(value = "账单类型（1-支付；2-退款）")
    private String transType;

    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;

    @ApiModelProperty(value = "比对状态（0-正常；1-异常）")
    private String compareStatus;

    @ApiModelProperty(value = "运营商ID")
    private String operatorId;
}