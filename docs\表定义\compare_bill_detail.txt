CREATE TABLE `compare_bill_exception_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `main_order_code` varchar(32) NOT NULL COMMENT '充电订单号',
  `chinaums_business_order_no` varchar(35) DEFAULT NULL COMMENT '银商交易订单号',
  `chinaums_trans_date` varchar(25) DEFAULT NULL COMMENT '银商交易时间',
  `chinaums_pay_amount` varchar(15) DEFAULT NULL COMMENT '银商交易金额',
  `ydpt_business_order_no` varchar(35) DEFAULT NULL COMMENT '移动平台交易订单号',
  `ydpt_trans_date` varchar(25) DEFAULT NULL COMMENT '移动平台交易时间',
  `ydpt_pay_amount` varchar(15) DEFAULT NULL COMMENT '移动平台交易金额',
  `nypt_business_order_no` varchar(35) DEFAULT NULL COMMENT '能源平台交易订单号',
  `nypt_trans_date` varchar(25) DEFAULT NULL COMMENT '能源平台交易时间',
  `nypt_pay_amount` varchar(15) DEFAULT NULL COMMENT '能源平台交易金额',
  `nebula_business_order_no` varchar(35) DEFAULT NULL COMMENT '星云交易订单号',
  `nebula_trans_date` varchar(25) DEFAULT NULL COMMENT '星云平台交易时间',
  `nebula_pay_amount` varchar(15) DEFAULT NULL COMMENT '星云平台交易金额',
  `etc_business_order_no` varchar(35) DEFAULT NULL COMMENT 'ETC交易订单号',
  `etc_trans_date` varchar(25) DEFAULT NULL COMMENT 'ETC交易时间',
  `etc_pay_amount` varchar(15) DEFAULT NULL COMMENT 'ETC交易金额',
  `balance_status` varchar(2) NOT NULL COMMENT '平账状态（0-未平账；1-已平账）',
  `balance_person` varchar(25) DEFAULT NULL COMMENT '平账人员',
  `balance_time` datetime DEFAULT NULL COMMENT '平账时间',
  `trans_type` varchar(2) NOT NULL COMMENT '交易类型（1-支付；2-退款）',
  `compare_type` varchar(2) NOT NULL COMMENT '比对类型（1-银商与移动支付平台支付账单比对；2-能源平台与星云支付账单比对；3-能源平台与移动支付平台支付账单比对；4-能源平台与发行ETC支付账单比对）',
  `compare_result` varchar(2) NOT NULL COMMENT '比对结果（0-正常；1-异常）',
  `version_no` varchar(25) NOT NULL COMMENT '版本号（N-1天格式yyyyMMdd）',
  `funds_from` int(11) NOT NULL,
  `operator_id` varchar(50) NOT NULL COMMENT '运营商ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `original_compare_result` varchar(15) DEFAULT NULL COMMENT '原始比对结果',
  `original_nypt_pay_amount` varchar(15) DEFAULT NULL COMMENT '原始能源平台金额',
  `original_chinaums_pay_amount` varchar(15) DEFAULT NULL COMMENT '原始银商金额',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `is_deleted` int(11) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `balance_status` (`balance_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8