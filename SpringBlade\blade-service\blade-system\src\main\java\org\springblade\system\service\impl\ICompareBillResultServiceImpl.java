/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;

import org.springblade.system.dto.RetryCompareDTO;
import org.springblade.system.entity.*;
import org.springblade.system.enums.ReconciliationCompareEnum;
import org.springblade.system.mapper.CompareBillResultMapper;
import org.springblade.system.service.*;
import org.springblade.system.vo.CompareBillDetailVO;
import org.springblade.system.vo.CompareBillResultVO;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
public class ICompareBillResultServiceImpl extends BaseServiceImpl<CompareBillResultMapper, CompareBillResult> implements ICompareBillResultService {

    @Autowired
    private ICompareBillDetailService compareBillDetailService;



    @Autowired
    private IChinaumsPayBillService chinaumsPayBillService;

    @Autowired
    private IChinaumsRefundBillService chinaumsRefundBillService;

    @Autowired
    private INyptPayBillService nyptPayBillService;

    @Autowired
    private INyptRefundBillService nyptRefundBillService;

    @Autowired
    private IReconciliationDuplicateCheckService duplicateCheckService;

    // 操作类型枚举
    private enum ReconciliationAction {
        NYPT_ADDED,          // 新增能源平台数据
        NYPT_REMOVED,         // 移除能源平台数据
        CHINAUMS_ADDED,       // 新增银商数据
        CHINAUMS_REMOVED,     // 移除银商数据
        BOTH_ADDED,           // 双方数据同时新增
        HISTORICAL_MATCH      // 历史记录匹配成功
    }

    @Override
    public IPage<CompareBillResultVO> selectCompareBillResultPage(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult) {
        return page.setRecords(baseMapper.selectCompareBillResultPage(page, compareBillResult));
    }

    @Override
    public IPage<CompareBillResultVO> selectCompareBillResultPage2(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult) {
        return page.setRecords(baseMapper.selectCompareBillResultPage2(page, compareBillResult));
    }

    @Override
    public byte[] exportData(CompareBillResultVO vo) {
        log.info("导出功能暂时不可用");
        throw new RuntimeException("导出功能暂时不可用，请联系管理员");
    }



    // 查询汇总记录
    @Override
    public List<CompareBillResultVO> querySummaryRecords(CompareBillResultVO vo) {
        List<CompareBillResultVO> allRecords = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 1000;

        while (true) {
            Page<CompareBillResultVO> page = new Page<>(pageNum, pageSize);
            IPage<CompareBillResultVO> result = this.selectCompareBillResultPage2(page, vo);
            if (result.getRecords() == null || result.getRecords().isEmpty()) break;
            allRecords.addAll(result.getRecords());
            pageNum++;
        }

        return allRecords;
    }

    // 定义金额获取的安全方法，统一返回 BigDecimal（金额保留两位小数）
    private BigDecimal getSafeBigDecimal(Object amountObj) {
        if (amountObj == null) {
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal value;
            if (amountObj instanceof BigDecimal) {
                value = (BigDecimal) amountObj;
            } else if (amountObj instanceof Number) {
                value = BigDecimal.valueOf(((Number) amountObj).doubleValue());
            } else if (amountObj instanceof String) {
                value = new BigDecimal((String) amountObj);
            } else {
                value = new BigDecimal(amountObj.toString());
            }
            return value.setScale(2, RoundingMode.HALF_UP); // 四舍五入到两位小数
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    // 计算汇总数据
    public Map<String, Object> calculateSummary(List<CompareBillResultVO> records) {
        Map<String, Object> summary = new HashMap<>();

        // 使用 BigDecimal 初始化所有汇总值
        BigDecimal payTotal = BigDecimal.ZERO;
        BigDecimal refundTotal = BigDecimal.ZERO;
        BigDecimal profit = BigDecimal.ZERO;
        BigDecimal nyptPayTotal = BigDecimal.ZERO;
        BigDecimal nyptRefundTotal = BigDecimal.ZERO;
        BigDecimal nyptProfit = BigDecimal.ZERO;
        BigDecimal chinaumsPayTotal = BigDecimal.ZERO;
        BigDecimal chinaumsRefundTotal = BigDecimal.ZERO;
        BigDecimal chinaumsProfit = BigDecimal.ZERO;
        boolean hasException = false;

        // 计算汇总值
        if (records != null && !records.isEmpty()) {
            hasException = records.stream().anyMatch(record ->
                    record.getCompareStatus() != null && "1".equals(record.getCompareStatus()));

            for (CompareBillResultVO record : records) {
                // 使用通用的金额获取方法
                BigDecimal nyptAmount = getSafeBigDecimal(record.getNyptAmount());
                BigDecimal chinaumsAmount = getSafeBigDecimal(record.getChinaumsAmount());

                if ("1".equals(record.getTransType())) { // 支付
                    payTotal = payTotal.add(nyptAmount); // 使用nyptAmount作为主金额
                    profit = profit.add(nyptAmount);
                    nyptPayTotal = nyptPayTotal.add(nyptAmount);
                    nyptProfit = nyptProfit.add(nyptAmount);
                    chinaumsPayTotal = chinaumsPayTotal.add(chinaumsAmount);
                    chinaumsProfit = chinaumsProfit.add(chinaumsAmount);
                } else if ("2".equals(record.getTransType())) { // 退款
                    refundTotal = refundTotal.add(nyptAmount); // 使用nyptAmount作为主金额
                    profit = profit.subtract(nyptAmount);
                    nyptRefundTotal = nyptRefundTotal.add(nyptAmount);
                    nyptProfit = nyptProfit.subtract(nyptAmount);
                    chinaumsRefundTotal = chinaumsRefundTotal.add(chinaumsAmount);
                    chinaumsProfit = chinaumsProfit.subtract(chinaumsAmount);
                }
            }
        }

        // 检查是否不匹配
        boolean payMismatch = nyptPayTotal.subtract(chinaumsPayTotal).abs()
                .compareTo(BigDecimal.valueOf(0.001)) > 0;
        boolean refundMismatch = nyptRefundTotal.subtract(chinaumsRefundTotal).abs()
                .compareTo(BigDecimal.valueOf(0.001)) > 0;
        boolean profitMismatch = nyptProfit.subtract(chinaumsProfit).abs()
                .compareTo(BigDecimal.valueOf(0.001)) > 0;

        // 存入结果（BigDecimal 保留两位小数后转 double，或直接存 BigDecimal）
        summary.put("payTotal", payTotal.doubleValue());
        summary.put("refundTotal", refundTotal.doubleValue());
        summary.put("profit", profit.doubleValue());
        summary.put("nyptPayTotal", nyptPayTotal.doubleValue());
        summary.put("nyptRefundTotal", nyptRefundTotal.doubleValue());
        summary.put("nyptProfit", nyptProfit.doubleValue());
        summary.put("chinaumsPayTotal", chinaumsPayTotal.doubleValue());
        summary.put("chinaumsRefundTotal", chinaumsRefundTotal.doubleValue());
        summary.put("chinaumsProfit", chinaumsProfit.doubleValue());
        summary.put("hasException", hasException);
        summary.put("payMismatch", payMismatch);
        summary.put("refundMismatch", refundMismatch);
        summary.put("profitMismatch", profitMismatch);

        return summary;
    }

    // 安全的时间范围处理
    private String getDateRange(CompareBillResultVO vo) {
        String start = vo.getChargeDateStart() != null ? vo.getChargeDateStart() : "";
        String end = vo.getChargeDateEnd() != null ? vo.getChargeDateEnd() : "";

        if (!start.isEmpty() && !end.isEmpty()) {
            return "(" + start + " 至 " + end + ")";
        }

        // 如果没有日期范围，使用默认（昨天）
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return "(" + yesterday.toString() + " 至 " + yesterday.toString() + ")";
    }

    // 创建高亮样式（红色字体）
    private CellStyle createHighlightStyle(SXSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        style.setFont(font);
        return style;
    }

    // 格式化金额
    private String formatAmount(double value) {
        return String.format("%.2f", value);
    }

    // 创建复杂表头（带位置参数）
    private void createDetailHeader(Sheet sheet, int startRow, CellStyle borderedStyle) {
        // 第一行表头
        Row headerRow1 = sheet.createRow(startRow);
        String[] headers1 = {
                "充电订单号",
                "能源平台", "", "",
                "对比结果",
                "银商", "", "",
                "交易类型",
//                "交易日期",
                "运营商",
                "支付渠道"
        };

        for (int i = 0; i < headers1.length; i++) {
            Cell cell = headerRow1.createCell(i);
            cell.setCellValue(headers1[i]);
            cell.setCellStyle(borderedStyle);
        }

        // 设置单元格合并
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 1, 3));  // 能源平台
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 5, 7));  // 银商

        // 第二行表头
        Row headerRow2 = sheet.createRow(startRow + 1);
        String[] headers2 = {
                "",
                "支付订单号", "交易时间", "交易金额",
                "",
                "支付订单号", "交易时间", "交易金额",
                "",
//                "",
                "",
                ""
        };

        for (int i = 0; i < headers2.length; i++) {
            Cell cell = headerRow2.createCell(i);
            cell.setCellValue(headers2[i]);
            cell.setCellStyle(borderedStyle);
        }
    }

    // 构建明细查询条件（简化版本）
    private void buildDetailQuery(CompareBillResultVO summaryVO, Object detailVO) {
        // 简化实现，避免依赖问题
        log.info("构建明细查询条件（简化版本）");
    }

    // 转换对比结果
    private String convertCompareResult(String resultCode) {
        if (resultCode == null) return "未知";

        switch (resultCode) {
            case "0": return "正常";
            case "1": return "异常";
            default: return "未知";
        }
    }

    // 转换交易类型
    private String convertTransType(String typeCode) {
        if (typeCode == null) return "其他";

        switch (typeCode) {
            case "1": return "支付";
            case "2": return "退款";
            default: return "其他";
        }
    }

    // 创建bu带边框的单元格样式
    private CellStyle createSummaryBorderedCellStyle(SXSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置细线边框样式
//        BorderStyle thin = BorderStyle.THIN;

        // 设置边框颜色（灰色）
//        IndexedColors grey = IndexedColors.GREY_80_PERCENT;

        // 设置四周边框
//        style.setBorderTop(thin);
//        style.setBorderBottom(thin);
//        style.setBorderLeft(thin);
//        style.setBorderRight(thin);

        // 设置边框颜色
//        style.setTopBorderColor(grey.getIndex());
//        style.setBottomBorderColor(grey.getIndex());
//        style.setLeftBorderColor(grey.getIndex());
//        style.setRightBorderColor(grey.getIndex());

        // 可选：设置水平和垂直居中
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    // 创建带边框的单元格样式
    private CellStyle createBorderedCellStyle(SXSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置细线边框样式
        BorderStyle thin = BorderStyle.THIN;

        // 设置边框颜色（灰色）
        IndexedColors grey = IndexedColors.GREY_80_PERCENT;

        // 设置四周边框
        style.setBorderTop(thin);
        style.setBorderBottom(thin);
        style.setBorderLeft(thin);
        style.setBorderRight(thin);

        // 设置边框颜色
        style.setTopBorderColor(grey.getIndex());
        style.setBottomBorderColor(grey.getIndex());
        style.setLeftBorderColor(grey.getIndex());
        style.setRightBorderColor(grey.getIndex());

        // 可选：设置水平和垂直居中
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    // 为表头应用边框样式
    private void applyBordersToHeader(Sheet sheet, int startRow, CellStyle borderedStyle) {
        // 表头占2行
        for (int r = startRow; r <= startRow + 1; r++) {
            Row row = sheet.getRow(r);
            if (row != null) {
                for (Cell cell : row) {
                    cell.setCellStyle(borderedStyle);
                }
            }
        }
    }



    // ======================= 能源平台 - 银商支付 对账相关 ======================================================

    @Override
    public boolean retry(RetryCompareDTO retryCompareDTO) {
        String startDateStr = retryCompareDTO.getStartDate();
        String endDateStr = retryCompareDTO.getEndDate();
        String compareType = retryCompareDTO.getCompareType();

        // 验证 compareType 是否有效
        if (compareType == null || ReconciliationCompareEnum.COMPARE_TYPE.getValue(compareType) == null) {
            throw new IllegalArgumentException("对比类型无效: " + compareType);
        }

        // 1. 解析日期
        LocalDate startDate;
        LocalDate endDate;
        try {
            startDate = LocalDate.parse(startDateStr);
            endDate = LocalDate.parse(endDateStr);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd", e);
        }

        // 2. 检查日期范围是否合理
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        // 获取当前日期作为基准（只计算一次）
        LocalDate today = LocalDate.now();
        boolean allSuccess = true;
        LocalDate currentDate = startDate;

        // 3. 循环处理每一天
        while (!currentDate.isAfter(endDate)) {
            // 如果当前日期是今天或将来，则跳出循环（因为只处理过去的）
            if (!currentDate.isBefore(today)) {
                break;
            }

            // 计算与基准日期的天数差（为负数，因为currentDate是过去）
            long daysBetween = ChronoUnit.DAYS.between(today, currentDate);
            int dateInt = (int) Math.abs(daysBetween); // 取绝对值

            try {
                if(compareType.equals(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())){
                    // 3.1 处理支付账单比对
                    nyptAndChinaumsPayBillCompare(dateInt);
                }
            } catch (Exception e) {
                log.error("支付账单比对失败【日期：{}】", currentDate, e);
                allSuccess = false;
            }

            try {
                if(compareType.equals(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())) {
                    // 3.2 处理退费账单比对
                    nyptAndChinaumsRefundBillCompare(dateInt);
                }
            } catch (Exception e) {
                log.error("退费账单比对失败【日期：{}】", currentDate, e);
                allSuccess = false;
            }

            currentDate = currentDate.plusDays(1); // 处理下一天
        }

        return allSuccess;
    }

    /**
     * 银商对账重试接口
     * 传入重试时间段（默认T-1），查找该时间内所有对比失败的记录（CompareBillDetail的compareResult为1），
     * 根据订单号重新对比，如果找到相同记录，然后对比金额，将对比结果更新到CompareBillDetail、CompareBillResult
     *
     * @param retryCompareDTO 重试参数
     * @return 重试结果
     */
    @Override
    public boolean retryFailedRecords(RetryCompareDTO retryCompareDTO) {
        String startDateStr = retryCompareDTO.getStartDate();
        String endDateStr = retryCompareDTO.getEndDate();
        String compareType = retryCompareDTO.getCompareType();

        log.info("银商对账重试开始，时间段：{} 至 {}，对比类型：{}", startDateStr, endDateStr, compareType);

        // 验证 compareType 是否有效
        if (compareType == null || ReconciliationCompareEnum.COMPARE_TYPE.getValue(compareType) == null) {
            throw new IllegalArgumentException("对比类型无效: " + compareType);
        }

        // 目前只支持能源平台与银商账单比对
        if (!ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode().equals(compareType)) {
            throw new IllegalArgumentException("当前只支持能源平台与银商账单比对");
        }

        // 1. 解析日期
        LocalDate startDate;
        LocalDate endDate;
        try {
            startDate = LocalDate.parse(startDateStr);
            endDate = LocalDate.parse(endDateStr);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd", e);
        }

        // 2. 检查日期范围是否合理
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        boolean allSuccess = true;

        try {
            // 3. 重试支付账单对比失败的记录
            boolean payRetryResult = retryFailedPayRecords(startDate, endDate);
            if (!payRetryResult) {
                allSuccess = false;
            }

            // 4. 重试退款账单对比失败的记录
            boolean refundRetryResult = retryFailedRefundRecords(startDate, endDate);
            if (!refundRetryResult) {
                allSuccess = false;
            }

        } catch (Exception e) {
            log.error("银商对账重试失败", e);
            allSuccess = false;
        }

        log.info("银商对账重试结束，结果：{}", allSuccess ? "成功" : "失败");
        return allSuccess;
    }

    public void nyptAndChinaumsPayBillCompare(int dateParamc) {
        log.info("能源平台和银商支付账单比对开始，入参：dateParamc:{}", dateParamc);

        // 获取当前版本号 (N-1日)
        LocalDate targetDate = LocalDate.now().minusDays(dateParamc);
        String currentVersion = targetDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询能源平台支付账单数据
        QueryWrapper<NyptPayBill> nyptPayBillWrapper = new QueryWrapper<>();
        nyptPayBillWrapper.eq("version_no", currentVersion);
        List<NyptPayBill> nyptPayBillList = nyptPayBillService.list(nyptPayBillWrapper);

        // 查询银商支付账单数据
        QueryWrapper<ChinaumsPayBill> chinaumsPayBillWrapper = new QueryWrapper<>();
        chinaumsPayBillWrapper.eq("version_no", currentVersion);
        List<ChinaumsPayBill> chinaumsPayBillList = chinaumsPayBillService.list(chinaumsPayBillWrapper);

        // 按 operator_id 分组处理
        Map<String, List<NyptPayBill>> nyptBillByOperator = nyptPayBillList.stream()
                .collect(Collectors.groupingBy(NyptPayBill::getOperatorId));

        Map<String, List<ChinaumsPayBill>> chinaumsBillByOperator = chinaumsPayBillList.stream()
                .collect(Collectors.groupingBy(ChinaumsPayBill::getOperatorId));

        // 获取所有涉及的operator_id
        Set<String> allOperators = new HashSet<>();
        allOperators.addAll(nyptBillByOperator.keySet());
        allOperators.addAll(chinaumsBillByOperator.keySet());

        // 处理每个operator的对账
        for (String operatorId : allOperators) {
            // 获取或创建CompareBillResult
            CompareBillResult compareBillResult = getOrCreateCompareBillResult(operatorId, currentVersion, ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode());

            // 初始化异常计数器
            compareBillResult.setAbnormalCount(0);

            // 获取当前operator的账单数据
            List<NyptPayBill> operatorNyptBills = nyptBillByOperator.getOrDefault(operatorId, Collections.emptyList());
            List<ChinaumsPayBill> operatorChinaumsBills = chinaumsBillByOperator.getOrDefault(operatorId, Collections.emptyList());

            // 更新账单统计信息
            updatePayBillStatistics(compareBillResult, operatorNyptBills, operatorChinaumsBills);

            // 处理对账逻辑
            processPayReconciliation(compareBillResult, operatorNyptBills, operatorChinaumsBills, currentVersion);

            // 保存或更新CompareBillResult
            saveOrUpdateCompareBillResult(compareBillResult);

            log.info("商户[{}]对账完成，异常记录数: {}", operatorId, compareBillResult.getAbnormalCount());
        }

        log.info("能源平台和银商退款账单比对结束");
    }

    public void nyptAndChinaumsRefundBillCompare(int dateParamc) {
        log.info("能源平台和银商退款账单比对开始，入参：dateParamc:{}", dateParamc);

        // 获取当前版本号 (N-1日)
        LocalDate targetDate = LocalDate.now().minusDays(dateParamc);
        String currentVersion = targetDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询能源平台退款账单数据
        QueryWrapper<NyptRefundBill> nyptRefundBillWrapper = new QueryWrapper<>();
//            nyptRefundBillWrapper.eq("user_id", nyptConfig.getUserId()); // todo 银商是否有特殊user_id
        nyptRefundBillWrapper.eq("version_no", currentVersion);
        List<NyptRefundBill> nyptRefundBillList = nyptRefundBillService.list(nyptRefundBillWrapper);

        // 查询银商退款账单数据
        QueryWrapper<ChinaumsRefundBill> chinaumsRefundBillWrapper = new QueryWrapper<>();
        chinaumsRefundBillWrapper.eq("version_no", currentVersion);
        List<ChinaumsRefundBill> chinaumsRefundBillList = chinaumsRefundBillService.list(chinaumsRefundBillWrapper);

        // 按 operator_id 分组处理
        Map<String, List<NyptRefundBill>> nyptBillByOperator = nyptRefundBillList.stream()
                .collect(Collectors.groupingBy(NyptRefundBill::getOperatorId));

        Map<String, List<ChinaumsRefundBill>> chinaumsBillByOperator = chinaumsRefundBillList.stream()
                .collect(Collectors.groupingBy(ChinaumsRefundBill::getOperatorId));

        // 获取所有涉及的operator_id
        Set<String> allOperators = new HashSet<>();
        allOperators.addAll(nyptBillByOperator.keySet());
        allOperators.addAll(chinaumsBillByOperator.keySet());

        // 处理每个operator的对账
        for (String operatorId : allOperators) {
            // 获取或创建CompareBillResult
            CompareBillResult compareBillResult = getOrCreateCompareBillResult(operatorId, currentVersion, ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());

            // 初始化异常计数器
            compareBillResult.setAbnormalCount(0);

            // 获取当前operator的账单数据
            List<NyptRefundBill> operatorNyptBills = nyptBillByOperator.getOrDefault(operatorId, Collections.emptyList());
            List<ChinaumsRefundBill> operatorChinaumsBills = chinaumsBillByOperator.getOrDefault(operatorId, Collections.emptyList());

            // 更新账单统计信息
            updateRefundBillStatistics(compareBillResult, operatorNyptBills, operatorChinaumsBills);

            // 处理对账逻辑
            processRefundReconciliation(compareBillResult, operatorNyptBills, operatorChinaumsBills, currentVersion);

            // 保存或更新CompareBillResult
            saveOrUpdateCompareBillResult(compareBillResult);

            log.info("商户[{}]对账完成，异常记录数: {}", operatorId, compareBillResult.getAbnormalCount());
        }

        log.info("能源平台和银商退款账单比对结束");
    }

    // 获取或创建CompareBillResult
    private CompareBillResult getOrCreateCompareBillResult(String operatorId, String versionNo, String transType) {
        // 解析原始字符串为 LocalDate 对象
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate date = LocalDate.parse(versionNo, inputFormatter);

        // 转换为 yyyy-MM-dd 格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = date.format(outputFormatter);

        QueryWrapper<CompareBillResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("trans_type", transType)
                .eq("operator_id", operatorId)
                .eq("charge_date", formattedDate);

        CompareBillResult result = this.getOne(queryWrapper);

        if (result == null) {
            result = new CompareBillResult();
            result.setOperatorId(operatorId);
            result.setChargeDate(formattedDate);
            result.setCompareStatus(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            result.setTransType(transType);
        }

        return result;
    }

    // 更新支付账单统计信息
    private void updatePayBillStatistics(CompareBillResult compareBillResult,
                                         List<NyptPayBill> nyptBills,
                                         List<ChinaumsPayBill> chinaumsBills) {
        // 更新能源平台统计
        int nyptTotal = nyptBills.size();
        compareBillResult.setNyptTotal(nyptTotal);
        BigDecimal nyptAmount = nyptBills.stream()
                .map(bill -> new BigDecimal(bill.getPayAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        compareBillResult.setNyptAmount(nyptAmount);

        // 更新银商统计
        int chinaumsTotal = chinaumsBills.size();
        compareBillResult.setChinaumsTotal(chinaumsTotal);
        BigDecimal chinaumsAmount = chinaumsBills.stream()
                .map(bill -> new BigDecimal(bill.getPayAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        compareBillResult.setChinaumsAmount(chinaumsAmount);
    }

    // 更新退费账单统计信息
    private void updateRefundBillStatistics(CompareBillResult compareBillResult,
                                            List<NyptRefundBill> nyptBills,
                                            List<ChinaumsRefundBill> chinaumsBills) {
        // 更新能源平台统计
        int nyptTotal = nyptBills.size();
        compareBillResult.setNyptTotal(nyptTotal);
        BigDecimal nyptAmount = nyptBills.stream()
                .map(bill -> new BigDecimal(bill.getRefundAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        compareBillResult.setNyptAmount(nyptAmount);

        // 更新银商统计
        int chinaumsTotal = chinaumsBills.size();
        compareBillResult.setChinaumsTotal(chinaumsTotal);
        BigDecimal chinaumsAmount = chinaumsBills.stream()
                .map(bill -> new BigDecimal(bill.getRefundAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        compareBillResult.setChinaumsAmount(chinaumsAmount);
    }

    // 处理支付账单对账逻辑
    private void processPayReconciliation(CompareBillResult compareBillResult,
                                          List<NyptPayBill> nyptBills,
                                          List<ChinaumsPayBill> chinaumsBills,
                                          String currentVersion) {
        // 支付订单号通常是唯一的，但为了提高对账准确性，也加入时间验证
        Map<String, List<ChinaumsPayBill>> chinaumsBillMap = chinaumsBills.stream()
                .collect(Collectors.groupingBy(ChinaumsPayBill::getBusinessOrderNo));

        // 处理能源平台数据
        for (NyptPayBill nyptBill : nyptBills) {
            String businessOrderNo = nyptBill.getBusinessOrderNo();
            List<ChinaumsPayBill> chinaumsBillList = chinaumsBillMap.get(businessOrderNo);

            if (chinaumsBillList != null && !chinaumsBillList.isEmpty()) {
                // 双方都存在，选择最佳匹配
                ChinaumsPayBill bestMatch = findBestPayMatch(nyptBill, chinaumsBillList);
                handlePayBothExist(nyptBill, bestMatch, currentVersion, compareBillResult);

                // 从列表中移除已匹配的记录
                chinaumsBillList.remove(bestMatch);
                if (chinaumsBillList.isEmpty()) {
                    chinaumsBillMap.remove(businessOrderNo);
                }
            } else {
                // 仅能源平台存在
                handlePayNyptOnly(nyptBill, currentVersion, compareBillResult);
            }
        }

        // 处理剩余银商数据（仅银商存在）
        for (List<ChinaumsPayBill> chinaumsBillList : chinaumsBillMap.values()) {
            for (ChinaumsPayBill chinaumsBill : chinaumsBillList) {
                handlePayChinaumsOnly(chinaumsBill, currentVersion, compareBillResult);
            }
        }
    }

    /**
     * 为能源平台支付记录找到最佳匹配的银商支付记录
     * 优先匹配金额相同的记录，其次考虑时间接近的记录
     */
    private ChinaumsPayBill findBestPayMatch(NyptPayBill nyptBill, List<ChinaumsPayBill> chinaumsPayList) {
        if (chinaumsPayList.isEmpty()) {
            return null;
        }

        if (chinaumsPayList.size() == 1) {
            return chinaumsPayList.get(0);
        }

        BigDecimal nyptAmount = new BigDecimal(nyptBill.getPayAmount());
        String nyptTransDate = nyptBill.getTransDate();

        // 第一优先级：金额完全匹配的记录
        List<ChinaumsPayBill> amountMatches = chinaumsPayList.stream()
                .filter(bill -> {
                    try {
                        BigDecimal chinaumsAmount = new BigDecimal(bill.getPayAmount());
                        return nyptAmount.compareTo(chinaumsAmount) == 0;
                    } catch (Exception e) {
                        log.warn("解析银商支付金额失败：{}", bill.getPayAmount());
                        return false;
                    }
                })
                .collect(Collectors.toList());

        if (amountMatches.size() == 1) {
            // 只有一个金额匹配的记录，直接返回
            return amountMatches.get(0);
        } else if (amountMatches.size() > 1) {
            // 多个金额匹配的记录，选择时间最接近的
            return findClosestPayTimeMatch(nyptTransDate, amountMatches);
        }

        // 第二优先级：如果没有金额匹配的记录，选择时间最接近的记录
        return findClosestPayTimeMatch(nyptTransDate, chinaumsPayList);
    }

    /**
     * 从银商支付记录列表中找到时间最接近的记录
     */
    private ChinaumsPayBill findClosestPayTimeMatch(String nyptTransDate, List<ChinaumsPayBill> chinaumsPayList) {
        if (chinaumsPayList.isEmpty()) {
            return null;
        }

        if (chinaumsPayList.size() == 1) {
            return chinaumsPayList.get(0);
        }

        // 解析能源平台交易时间
        Date nyptDate = parseRefundDate(nyptTransDate); // 复用退费的日期解析方法
        if (nyptDate == null) {
            // 如果时间解析失败，返回第一个记录
            return chinaumsPayList.get(0);
        }

        ChinaumsPayBill closestMatch = null;
        long minTimeDiff = Long.MAX_VALUE;

        for (ChinaumsPayBill chinaumsBill : chinaumsPayList) {
            Date chinaumsDate = parseRefundDate(chinaumsBill.getTransDate());
            if (chinaumsDate != null) {
                long timeDiff = Math.abs(nyptDate.getTime() - chinaumsDate.getTime());
                if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    closestMatch = chinaumsBill;
                }
            }
        }

        return closestMatch != null ? closestMatch : chinaumsPayList.get(0);
    }

    // 处理退款账单对账逻辑
    private void processRefundReconciliation(CompareBillResult compareBillResult,
                                             List<NyptRefundBill> nyptBills,
                                             List<ChinaumsRefundBill> chinaumsBills,
                                             String currentVersion) {
        // 根据需求，银商系统的退费订单号与支付订单号相同，同一订单的多笔退费使用相同的退费订单号
        // 因此需要按主订单号分组处理，并考虑时间匹配

        // 按主订单号分组能源平台退费记录
        Map<String, List<NyptRefundBill>> nyptBillsByMainOrder = nyptBills.stream()
                .collect(Collectors.groupingBy(NyptRefundBill::getMainOrderCode));

        // 按退费ID（实际是支付订单号）分组银商退费记录
        Map<String, List<ChinaumsRefundBill>> chinaumsBillsByRefundId = chinaumsBills.stream()
                .collect(Collectors.groupingBy(ChinaumsRefundBill::getRefundId));

        // 处理能源平台数据
        for (Map.Entry<String, List<NyptRefundBill>> entry : nyptBillsByMainOrder.entrySet()) {
            String mainOrderCode = entry.getKey();
            List<NyptRefundBill> nyptRefundList = entry.getValue();

            // 查找对应的银商退费记录（通过支付订单号匹配）
            String payOrderNo = getPayOrderNoByMainOrder(mainOrderCode);
            List<ChinaumsRefundBill> chinaumsRefundList = chinaumsBillsByRefundId.get(payOrderNo);

            if (chinaumsRefundList != null && !chinaumsRefundList.isEmpty()) {
                // 双方都存在退费记录，进行智能匹配
                handleRefundBothExistWithTimeMatching(nyptRefundList, chinaumsRefundList, currentVersion, compareBillResult);
                // 从map中移除已匹配的记录
                chinaumsBillsByRefundId.remove(payOrderNo);
            } else {
                // 仅能源平台存在
                for (NyptRefundBill nyptBill : nyptRefundList) {
                    handleRefundNyptOnly(nyptBill, currentVersion, compareBillResult);
                }
            }
        }

        // 处理剩余银商数据（仅银商存在）
        for (List<ChinaumsRefundBill> chinaumsRefundList : chinaumsBillsByRefundId.values()) {
            for (ChinaumsRefundBill chinaumsBill : chinaumsRefundList) {
                handleRefundChinaumsOnly(chinaumsBill, currentVersion, compareBillResult);
            }
        }
    }

    /**
     * 根据主订单号获取支付订单号
     * 从能源平台支付账单中查找对应的支付订单号
     */
    private String getPayOrderNoByMainOrder(String mainOrderCode) {
        QueryWrapper<NyptPayBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_order_code", mainOrderCode)
                .orderByDesc("create_time")
                .last("LIMIT 1");

        NyptPayBill payBill = nyptPayBillService.getOne(queryWrapper);
        return payBill != null ? payBill.getBusinessOrderNo() : null;
    }

    /**
     * 处理双方都存在退费记录的情况，使用时间匹配优化对账准确性
     */
    private void handleRefundBothExistWithTimeMatching(List<NyptRefundBill> nyptRefundList,
                                                       List<ChinaumsRefundBill> chinaumsRefundList,
                                                       String currentVersion,
                                                       CompareBillResult compareBillResult) {
        log.info("开始处理双方退费记录匹配，能源平台记录数：{}，银商记录数：{}",
                nyptRefundList.size(), chinaumsRefundList.size());

        // 创建银商记录的副本，用于匹配过程中的移除操作
        List<ChinaumsRefundBill> availableChinaumsBills = new ArrayList<>(chinaumsRefundList);

        for (NyptRefundBill nyptBill : nyptRefundList) {
            ChinaumsRefundBill bestMatch = findBestRefundMatch(nyptBill, availableChinaumsBills);

            if (bestMatch != null) {
                // 找到匹配记录，创建对账明细
                handleRefundBothExist(nyptBill, bestMatch, currentVersion, compareBillResult);
                // 从可用列表中移除已匹配的记录
                availableChinaumsBills.remove(bestMatch);
            } else {
                // 没有找到匹配记录，作为仅能源平台存在处理
                handleRefundNyptOnly(nyptBill, currentVersion, compareBillResult);
            }
        }

        // 处理剩余未匹配的银商记录
        for (ChinaumsRefundBill chinaumsBill : availableChinaumsBills) {
            handleRefundChinaumsOnly(chinaumsBill, currentVersion, compareBillResult);
        }
    }

    /**
     * 为能源平台退费记录找到最佳匹配的银商退费记录
     * 优先匹配金额相同的记录，其次考虑时间接近的记录
     */
    private ChinaumsRefundBill findBestRefundMatch(NyptRefundBill nyptBill, List<ChinaumsRefundBill> chinaumsRefundList) {
        if (chinaumsRefundList.isEmpty()) {
            return null;
        }

        BigDecimal nyptAmount = new BigDecimal(nyptBill.getRefundAmount());
        String nyptRefundDate = nyptBill.getRefundDate();

        // 第一优先级：金额完全匹配的记录
        List<ChinaumsRefundBill> amountMatches = chinaumsRefundList.stream()
                .filter(bill -> {
                    try {
                        BigDecimal chinaumsAmount = new BigDecimal(bill.getRefundAmount());
                        return nyptAmount.compareTo(chinaumsAmount) == 0;
                    } catch (Exception e) {
                        log.warn("解析银商退费金额失败：{}", bill.getRefundAmount());
                        return false;
                    }
                })
                .collect(Collectors.toList());

        if (amountMatches.size() == 1) {
            // 只有一个金额匹配的记录，直接返回
            return amountMatches.get(0);
        } else if (amountMatches.size() > 1) {
            // 多个金额匹配的记录，选择时间最接近的
            return findClosestTimeMatch(nyptRefundDate, amountMatches);
        }

        // 第二优先级：如果没有金额匹配的记录，选择时间最接近的记录
        return findClosestTimeMatch(nyptRefundDate, chinaumsRefundList);
    }

    /**
     * 从银商退费记录列表中找到时间最接近的记录
     */
    private ChinaumsRefundBill findClosestTimeMatch(String nyptRefundDate, List<ChinaumsRefundBill> chinaumsRefundList) {
        if (chinaumsRefundList.isEmpty()) {
            return null;
        }

        if (chinaumsRefundList.size() == 1) {
            return chinaumsRefundList.get(0);
        }

        // 解析能源平台退费时间
        Date nyptDate = parseRefundDate(nyptRefundDate);
        if (nyptDate == null) {
            // 如果时间解析失败，返回第一个记录
            return chinaumsRefundList.get(0);
        }

        ChinaumsRefundBill closestMatch = null;
        long minTimeDiff = Long.MAX_VALUE;

        for (ChinaumsRefundBill chinaumsBill : chinaumsRefundList) {
            Date chinaumsDate = parseRefundDate(chinaumsBill.getRefundDate());
            if (chinaumsDate != null) {
                long timeDiff = Math.abs(nyptDate.getTime() - chinaumsDate.getTime());
                if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    closestMatch = chinaumsBill;
                }
            }
        }

        return closestMatch != null ? closestMatch : chinaumsRefundList.get(0);
    }

    /**
     * 解析退费日期字符串为Date对象
     * 支持多种日期格式
     */
    private Date parseRefundDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 常见的日期格式
        String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd",
                "yyyyMMdd HH:mm:ss",
                "yyyyMMdd"
        };

        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr);
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }

        log.warn("无法解析退费日期格式：{}", dateStr);
        return null;
    }

    /**
     * 检查两个日期是否在同一天
     */
    private boolean isSameDay(String date1, String date2) {
        Date d1 = parseRefundDate(date1);
        Date d2 = parseRefundDate(date2);

        if (d1 == null || d2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(d1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(d2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 保存或更新对账明细，避免重复记录
     */
    private CompareBillDetail saveOrUpdateCompareBillDetail(CompareBillDetail detail) {
        // 检查银商唯一索引是否存在重复
        if (StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo()) &&
            StringUtils.isNotBlank(detail.getChinaumsSearchNo())) {
            CompareBillDetail existingByChinaums = duplicateCheckService.findExistingDetailByChinaums(
                detail.getChinaumsBusinessOrderNo(),
                detail.getChinaumsSearchNo(),
                detail.getCompareType(),
                detail.getTransType()
            );

            if (existingByChinaums != null) {
                log.info("发现银商重复记录，更新现有记录，ID：{}", existingByChinaums.getId());
                CompareBillDetail updated = duplicateCheckService.updateExistingDetail(existingByChinaums, detail);
                compareBillDetailService.updateById(updated);
                return updated;
            }
        }

        // 检查能源平台唯一索引是否存在重复
        if (StringUtils.isNotBlank(detail.getNyptBusinessOrderNo()) &&
            StringUtils.isNotBlank(detail.getNyptRefundId())) {
            CompareBillDetail existingByNypt = duplicateCheckService.findExistingDetailByNypt(
                detail.getNyptBusinessOrderNo(),
                detail.getNyptRefundId(),
                detail.getCompareType(),
                detail.getTransType()
            );

            if (existingByNypt != null) {
                log.info("发现能源平台重复记录，更新现有记录，ID：{}", existingByNypt.getId());
                CompareBillDetail updated = duplicateCheckService.updateExistingDetail(existingByNypt, detail);
                compareBillDetailService.updateById(updated);
                return updated;
            }
        }

        // 没有重复记录，直接保存
        compareBillDetailService.save(detail);
        return detail;
    }

    // 更新CompareBillResult基于对账明细
    private void updateCompareResultForDetail(CompareBillResult compareBillResult,
                                              CompareBillDetail detail,
                                              ReconciliationAction action) {
        // 处理异常计数
        if (ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode().equals(detail.getCompareResult())) {
            compareBillResult.setAbnormalCount(compareBillResult.getAbnormalCount() + 1);
            compareBillResult.setCompareStatus(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
        }

        // 处理历史平账记录的异常计数调整
        if (ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode().equals(detail.getBalanceStatus()) &&
                ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode().equals(detail.getOriginalCompareResult())) {
            compareBillResult.setAbnormalCount(Math.max(0, compareBillResult.getAbnormalCount() - 1));

            if (compareBillResult.getAbnormalCount() == 0) {
                compareBillResult.setCompareStatus(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            }
        }

        // 根据操作类型更新账单统计数据
        switch (action) {
            case NYPT_ADDED:
                // 新增能源平台数据（当前版本）
                compareBillResult.setNyptTotal(compareBillResult.getNyptTotal() + 1);
                compareBillResult.setNyptAmount(compareBillResult.getNyptAmount()
                        .add(new BigDecimal(detail.getNyptPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                break;

            case NYPT_REMOVED:
                // 移除能源平台数据（历史平账）
                compareBillResult.setNyptTotal(Math.max(0, compareBillResult.getNyptTotal() - 1));
                compareBillResult.setNyptAmount(compareBillResult.getNyptAmount()
                        .subtract(new BigDecimal(detail.getNyptPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                break;

            case CHINAUMS_ADDED:
                // 新增银商数据（当前版本）
                compareBillResult.setChinaumsTotal(compareBillResult.getChinaumsTotal() + 1);
                compareBillResult.setChinaumsAmount(compareBillResult.getChinaumsAmount()
                        .add(new BigDecimal(detail.getNebulaPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                break;

            case CHINAUMS_REMOVED:
                // 移除银商数据（历史平账）
                compareBillResult.setChinaumsTotal(Math.max(0, compareBillResult.getChinaumsTotal() - 1));
                compareBillResult.setChinaumsAmount(compareBillResult.getChinaumsAmount()
                        .subtract(new BigDecimal(detail.getNebulaPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                break;

            case BOTH_ADDED:
                // 双方数据同时新增（正常情况）
                compareBillResult.setNyptTotal(compareBillResult.getNyptTotal() + 1);
                compareBillResult.setNyptAmount(compareBillResult.getNyptAmount()
                        .add(new BigDecimal(detail.getNyptPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                compareBillResult.setChinaumsTotal(compareBillResult.getChinaumsTotal() + 1);
                compareBillResult.setChinaumsAmount(compareBillResult.getChinaumsAmount()
                        .add(new BigDecimal(detail.getNebulaPayAmount()))
                        .setScale(2, RoundingMode.HALF_UP));
                break;

            case HISTORICAL_MATCH:
                // 历史记录匹配成功（跨版本平账）
                // 不需要更新总数，因为历史数据已在之前版本统计
                // 但需要更新金额（如果金额不一致）
                BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                BigDecimal chinaumsAmount = new BigDecimal(detail.getNebulaPayAmount());

                if (!nyptAmount.equals(chinaumsAmount)) {
                    // 调整金额差异
                    BigDecimal diff = nyptAmount.subtract(new BigDecimal(detail.getOriginalNyptPayAmount()));
                    compareBillResult.setNyptAmount(compareBillResult.getNyptAmount().add(diff));

                    diff = chinaumsAmount.subtract(new BigDecimal(detail.getOriginalChinaumsPayAmount()));
                    compareBillResult.setChinaumsAmount(compareBillResult.getChinaumsAmount().add(diff));
                }
                break;
        }

        // 确保金额不为负数
        if (compareBillResult.getNyptAmount().compareTo(BigDecimal.ZERO) < 0) {
            compareBillResult.setNyptAmount(BigDecimal.ZERO);
        }
        if (compareBillResult.getChinaumsAmount().compareTo(BigDecimal.ZERO) < 0) {
            compareBillResult.setChinaumsAmount(BigDecimal.ZERO);
        }
    }

    // 保存或更新CompareBillResult
    private void saveOrUpdateCompareBillResult(CompareBillResult compareBillResult) {
        if (compareBillResult.getId() == null) {
            this.save(compareBillResult);
        } else {
            this.updateById(compareBillResult);
        }
    }

    // 处理双方数据都存在的情况
    private CompareBillDetail handlePayBothExist(NyptPayBill nyptBill, ChinaumsPayBill chinaumsBill, String currentVersion, CompareBillResult compareBillResult) {
        BigDecimal nyptAmount = new BigDecimal(nyptBill.getPayAmount());
        BigDecimal chinaumsAmount = new BigDecimal(chinaumsBill.getPayAmount());

        CompareBillDetail detail = new CompareBillDetail();
        // 设置能源平台数据
        detail.setNyptBusinessOrderNo(nyptBill.getBusinessOrderNo());
        detail.setNyptTransDate(nyptBill.getTransDate());
        detail.setNyptPayAmount(nyptBill.getPayAmount());
        detail.setNyptRefundId(nyptBill.getBusinessOrderNo()); // 支付记录的退费ID使用订单号
        detail.setMainOrderCode(nyptBill.getMainOrderCode());
        detail.setOperatorId(nyptBill.getOperatorId());
        detail.setFundsFrom(nyptBill.getFundsFrom());

        // 设置银商数据
        detail.setChinaumsBusinessOrderNo(chinaumsBill.getBusinessOrderNo());
        detail.setChinaumsTransDate(chinaumsBill.getTransDate());
        detail.setChinaumsPayAmount(chinaumsBill.getPayAmount());
        detail.setChinaumsSearchNo(chinaumsBill.getSearchNo());

        // 公共字段设置
        detail.setVersionNo(currentVersion);
        detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode());
        detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());

        if (nyptAmount.compareTo(chinaumsAmount) == 0) {
            // 金额一致，平账
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
        } else {
            // 金额不一致
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());

            log.warn("金额不一致：商户[{}] 能源订单[{}]金额={}，银商订单[{}]金额={}",
                    nyptBill.getOperatorId(), nyptBill.getBusinessOrderNo(), nyptAmount,
                    chinaumsBill.getBusinessOrderNo(), chinaumsAmount);
        }

        // 检查是否存在重复记录并保存对账明细
        CompareBillDetail savedDetail = saveOrUpdateCompareBillDetail(detail);

        ReconciliationAction action = ReconciliationAction.BOTH_ADDED;
        updateCompareResultForDetail(compareBillResult, savedDetail, action);

        return savedDetail;
    }

    // 处理仅能源平台存在的数据
    private CompareBillDetail handlePayNyptOnly(NyptPayBill nyptBill, String currentVersion, CompareBillResult compareBillResult) {
        // 查询历史未平账记录
        QueryWrapper<CompareBillDetail> historyQuery = new QueryWrapper<>();
        historyQuery.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("chinaums_business_order_no", nyptBill.getBusinessOrderNo())
                .eq("balance_status", ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode())
                .isNull("nypt_business_order_no");

        CompareBillDetail detail = compareBillDetailService.getOne(historyQuery);
        ReconciliationAction action = ReconciliationAction.NYPT_ADDED;

        if (detail != null) {
            // 保存原始值
            detail.setOriginalCompareResult(detail.getCompareResult());
            detail.setOriginalNyptPayAmount(detail.getNyptPayAmount());
            detail.setOriginalChinaumsPayAmount(detail.getChinaumsPayAmount());

            // 更新记录
            BigDecimal historyAmount = new BigDecimal(detail.getChinaumsPayAmount());
            BigDecimal currentAmount = new BigDecimal(nyptBill.getPayAmount());

            detail.setNyptBusinessOrderNo(nyptBill.getBusinessOrderNo());
            detail.setNyptTransDate(nyptBill.getTransDate());
            detail.setNyptPayAmount(nyptBill.getPayAmount());
            detail.setMainOrderCode(nyptBill.getMainOrderCode());
            detail.setOperatorId(nyptBill.getOperatorId());
            detail.setFundsFrom(nyptBill.getFundsFrom());
            detail.setVersionNo(currentVersion);

            if (historyAmount.compareTo(currentAmount) == 0) {
                detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            } else {
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            }

            compareBillDetailService.updateById(detail);

            // 获取历史未平账数据对应的对账结果数据
            CompareBillResult historyCompareBillResult = getOrCreateCompareBillResult(detail.getOperatorId(), detail.getVersionNo(), detail.getTransType());

            // 更新历史未平账数据结果表
            action = ReconciliationAction.NYPT_REMOVED;
            updateCompareResultForDetail(historyCompareBillResult, detail, action);
            saveOrUpdateCompareBillResult(historyCompareBillResult);

            action = ReconciliationAction.HISTORICAL_MATCH;
        } else {
            // 无匹配历史记录，创建新异常记录
            detail = new CompareBillDetail();
            detail.setNyptBusinessOrderNo(nyptBill.getBusinessOrderNo());
            detail.setNyptTransDate(nyptBill.getTransDate());
            detail.setNyptPayAmount(nyptBill.getPayAmount());
            detail.setMainOrderCode(nyptBill.getMainOrderCode());
            detail.setOperatorId(nyptBill.getOperatorId());
            detail.setFundsFrom(nyptBill.getFundsFrom());
            detail.setVersionNo(currentVersion);
            detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode());
            detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            detail.setOriginalCompareResult(null); // 新记录没有原始状态
            compareBillDetailService.save(detail);
        }

        updateCompareResultForDetail(compareBillResult, detail, action);
        return detail;
    }

    // 处理仅银商存在的数据
    private CompareBillDetail handlePayChinaumsOnly(ChinaumsPayBill chinaumsBill, String currentVersion, CompareBillResult compareBillResult) {
        // 查询历史未平账记录
        QueryWrapper<CompareBillDetail> historyQuery = new QueryWrapper<>();
        historyQuery.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("nypt_business_order_no", chinaumsBill.getBusinessOrderNo())
                .eq("balance_status", ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode())
                .isNull("chinaums_business_order_no");

        CompareBillDetail detail = compareBillDetailService.getOne(historyQuery);
        ReconciliationAction action = ReconciliationAction.CHINAUMS_ADDED;

        if (detail != null) {
            // 保存原始值
            detail.setOriginalCompareResult(detail.getCompareResult());
            detail.setOriginalNyptPayAmount(detail.getNyptPayAmount());
            detail.setOriginalChinaumsPayAmount(detail.getChinaumsPayAmount());

            // 更新记录
            BigDecimal historyAmount = new BigDecimal(detail.getNyptPayAmount());
            BigDecimal currentAmount = new BigDecimal(chinaumsBill.getPayAmount());

            detail.setChinaumsBusinessOrderNo(chinaumsBill.getBusinessOrderNo());
            detail.setChinaumsTransDate(chinaumsBill.getTransDate());
            detail.setChinaumsPayAmount(chinaumsBill.getPayAmount());

            if (historyAmount.compareTo(currentAmount) == 0) {
                detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            } else {
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            }

            compareBillDetailService.updateById(detail);

            // 获取历史未平账数据对应的对账结果数据
            CompareBillResult historyCompareBillResult = getOrCreateCompareBillResult(detail.getOperatorId(), detail.getVersionNo(), detail.getTransType());

            // 更新历史未平账数据结果表
            action = ReconciliationAction.CHINAUMS_REMOVED;
            updateCompareResultForDetail(historyCompareBillResult, detail, action);
            saveOrUpdateCompareBillResult(historyCompareBillResult);

            action = ReconciliationAction.HISTORICAL_MATCH;
        } else {
            // 创建新记录
            detail = new CompareBillDetail();
            detail.setChinaumsBusinessOrderNo(chinaumsBill.getBusinessOrderNo());
            detail.setChinaumsTransDate(chinaumsBill.getTransDate());
            detail.setChinaumsPayAmount(chinaumsBill.getPayAmount());
            detail.setOperatorId(chinaumsBill.getOperatorId());
//            detail.setFundsFrom(null);
            detail.setVersionNo(currentVersion);
            detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());
            detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            detail.setOriginalCompareResult(null); // 新记录没有原始状态
            compareBillDetailService.save(detail);
        }

        updateCompareResultForDetail(compareBillResult, detail, action);

        return detail;
    }

    // 处理双方数据都存在的情况
    private CompareBillDetail handleRefundBothExist(NyptRefundBill nyptBill, ChinaumsRefundBill chinaumsBill, String currentVersion, CompareBillResult compareBillResult) {
        BigDecimal nyptAmount = new BigDecimal(nyptBill.getRefundAmount());
        BigDecimal chinaumsAmount = new BigDecimal(chinaumsBill.getRefundAmount());

        CompareBillDetail detail = new CompareBillDetail();
        // 设置能源平台数据
        detail.setNyptBusinessOrderNo(nyptBill.getPayOrderNo()); // 使用支付订单号
        detail.setNyptTransDate(nyptBill.getRefundDate());
        detail.setNyptPayAmount(nyptBill.getRefundAmount());
        detail.setNyptRefundId(nyptBill.getRefundId()); // 设置退费ID
        detail.setMainOrderCode(nyptBill.getMainOrderCode());
        detail.setOperatorId(nyptBill.getOperatorId());
        detail.setFundsFrom(nyptBill.getFundsFrom());

        // 设置银商数据
        detail.setChinaumsBusinessOrderNo(chinaumsBill.getRefundId());
        detail.setChinaumsTransDate(chinaumsBill.getRefundDate());
        detail.setChinaumsPayAmount(chinaumsBill.getRefundAmount());
        detail.setChinaumsSearchNo(chinaumsBill.getSearchNo());

        // 公共字段设置
        detail.setVersionNo(currentVersion);
        detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());
        detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());

        if (nyptAmount.compareTo(chinaumsAmount) == 0) {
            // 金额一致，平账
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
        } else {
            // 金额不一致
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());

            log.warn("金额不一致：商户[{}] 能源订单[{}]金额={}，银商订单[{}]金额={}",
                    nyptBill.getOperatorId(), nyptBill.getRefundId(), nyptAmount,
                    chinaumsBill.getRefundId(), chinaumsAmount);
        }

        // 检查是否存在重复记录并保存对账明细
        CompareBillDetail savedDetail = saveOrUpdateCompareBillDetail(detail);

        ReconciliationAction action = ReconciliationAction.BOTH_ADDED;
        updateCompareResultForDetail(compareBillResult, savedDetail, action);

        return savedDetail;
    }

    // 处理仅能源平台存在的数据
    private CompareBillDetail handleRefundNyptOnly(NyptRefundBill nyptBill, String currentVersion, CompareBillResult compareBillResult) {
        // 查询历史未平账记录
        QueryWrapper<CompareBillDetail> historyQuery = new QueryWrapper<>();
        historyQuery.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("chinaums_business_order_no", nyptBill.getRefundId())
                .eq("balance_status", ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode())
                .isNull("nypt_business_order_no");

        CompareBillDetail detail = compareBillDetailService.getOne(historyQuery);
        ReconciliationAction action = ReconciliationAction.NYPT_ADDED;

        if (detail != null) {
            // 保存原始值
            detail.setOriginalCompareResult(detail.getCompareResult());
            detail.setOriginalNyptPayAmount(detail.getNyptPayAmount());
            detail.setOriginalChinaumsPayAmount(detail.getChinaumsPayAmount());

            // 更新记录
            BigDecimal historyAmount = new BigDecimal(detail.getChinaumsPayAmount());
            BigDecimal currentAmount = new BigDecimal(nyptBill.getRefundAmount());

            detail.setNyptBusinessOrderNo(nyptBill.getRefundId());
            detail.setNyptTransDate(nyptBill.getRefundDate());
            detail.setNyptPayAmount(nyptBill.getRefundAmount());
            detail.setMainOrderCode(nyptBill.getMainOrderCode());
            detail.setOperatorId(nyptBill.getOperatorId());
            detail.setFundsFrom(nyptBill.getFundsFrom());
            detail.setVersionNo(currentVersion);

            if (historyAmount.compareTo(currentAmount) == 0) {
                detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            } else {
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            }

            compareBillDetailService.updateById(detail);

            // 获取历史未平账数据对应的对账结果数据
            CompareBillResult historyCompareBillResult = getOrCreateCompareBillResult(detail.getOperatorId(), detail.getVersionNo(), detail.getTransType());

            // 更新历史未平账数据结果表
            action = ReconciliationAction.NYPT_REMOVED;
            updateCompareResultForDetail(historyCompareBillResult, detail, action);
            saveOrUpdateCompareBillResult(historyCompareBillResult);

            action = ReconciliationAction.HISTORICAL_MATCH;
        } else {
            // 无匹配历史记录，创建新异常记录
            detail = new CompareBillDetail();
            detail.setNyptBusinessOrderNo(nyptBill.getRefundId());
            detail.setNyptTransDate(nyptBill.getRefundDate());
            detail.setNyptPayAmount(nyptBill.getRefundAmount());
            detail.setMainOrderCode(nyptBill.getMainOrderCode());
            detail.setOperatorId(nyptBill.getOperatorId());
            detail.setFundsFrom(nyptBill.getFundsFrom());
            detail.setVersionNo(currentVersion);
            detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());
            detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            detail.setOriginalCompareResult(null); // 新记录没有原始状态
            compareBillDetailService.save(detail);
        }

        updateCompareResultForDetail(compareBillResult, detail, action);

        return detail;
    }

    // 处理仅银商存在的数据
    private CompareBillDetail handleRefundChinaumsOnly(ChinaumsRefundBill chinaumsBill, String currentVersion, CompareBillResult compareBillResult) {
        // 查询历史未平账记录
        QueryWrapper<CompareBillDetail> historyQuery = new QueryWrapper<>();
        historyQuery.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("nypt_business_order_no", chinaumsBill.getRefundId())
                .eq("balance_status", ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode())
                .isNull("chinaums_business_order_no");

        CompareBillDetail detail = compareBillDetailService.getOne(historyQuery);
        ReconciliationAction action = ReconciliationAction.CHINAUMS_ADDED;

        if (detail != null) {
            // 保存原始值
            detail.setOriginalCompareResult(detail.getCompareResult());
            detail.setOriginalNyptPayAmount(detail.getNyptPayAmount());
            detail.setOriginalChinaumsPayAmount(detail.getNebulaPayAmount());

            // 更新记录
            BigDecimal historyAmount = new BigDecimal(detail.getNyptPayAmount());
            BigDecimal currentAmount = new BigDecimal(chinaumsBill.getRefundAmount());

            detail.setChinaumsBusinessOrderNo(chinaumsBill.getRefundId());
            detail.setChinaumsTransDate(chinaumsBill.getRefundDate());
            detail.setChinaumsPayAmount(chinaumsBill.getRefundAmount());

            if (historyAmount.compareTo(currentAmount) == 0) {
                detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            } else {
                detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            }

            compareBillDetailService.updateById(detail);

            // 获取历史未平账数据对应的对账结果数据
            CompareBillResult historyCompareBillResult = getOrCreateCompareBillResult(detail.getOperatorId(), detail.getVersionNo(), detail.getTransType());

            // 更新历史未平账数据结果表
            action = ReconciliationAction.CHINAUMS_REMOVED;
            updateCompareResultForDetail(historyCompareBillResult, detail, action);
            saveOrUpdateCompareBillResult(historyCompareBillResult);

            action = ReconciliationAction.HISTORICAL_MATCH;
        } else {
            // 创建新记录
            detail = new CompareBillDetail();
            detail.setChinaumsBusinessOrderNo(chinaumsBill.getRefundId());
            detail.setChinaumsTransDate(chinaumsBill.getRefundDate());
            detail.setChinaumsPayAmount(chinaumsBill.getRefundAmount());
            detail.setOperatorId(chinaumsBill.getOperatorId());
//            detail.setFundsFrom(null);
            detail.setVersionNo(currentVersion);
            detail.setTransType(ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());
            detail.setCompareType(ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.UNFINISHED_ACCOUNTS.getCode());
            detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            detail.setOriginalCompareResult(null); // 新记录没有原始状态
            compareBillDetailService.save(detail);
        }

        updateCompareResultForDetail(compareBillResult, detail, action);

        return detail;
    }

    /**
     * 重试支付账单对比失败的记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 重试结果
     */
    private boolean retryFailedPayRecords(LocalDate startDate, LocalDate endDate) {
        log.info("开始重试支付账单对比失败的记录，时间段：{} 至 {}", startDate, endDate);

        try {
            // 1. 查询指定时间段内对比失败的支付记录
            List<CompareBillDetail> failedDetails = getFailedPayDetails(startDate, endDate);
            log.info("找到 {} 条支付对比失败记录", failedDetails.size());

            if (failedDetails.isEmpty()) {
                return true;
            }

            int successCount = 0;
            int totalCount = failedDetails.size();

            // 2. 逐个处理失败记录，基于订单号查找关联记录
            for (CompareBillDetail detail : failedDetails) {
                try {
                    if (retryFailedPayDetailByOrderNo(detail)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("重试支付记录失败，记录ID：{}，订单号：{}", detail.getId(),
                            detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo(), e);
                }
            }

            // 3. 更新所有相关运营商和版本的对账结果统计
            updateAllRelatedCompareBillResults(failedDetails, ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode());

            log.info("支付账单重试完成，成功：{}/{}", successCount, totalCount);
            return successCount == totalCount;

        } catch (Exception e) {
            log.error("重试支付账单对比失败记录时发生异常", e);
            return false;
        }
    }

    /**
     * 重试退款账单对比失败的记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 重试结果
     */
    private boolean retryFailedRefundRecords(LocalDate startDate, LocalDate endDate) {
        log.info("开始重试退款账单对比失败的记录，时间段：{} 至 {}", startDate, endDate);

        try {
            // 1. 查询指定时间段内对比失败的退款记录
            List<CompareBillDetail> failedDetails = getFailedRefundDetails(startDate, endDate);
            log.info("找到 {} 条退款对比失败记录", failedDetails.size());

            if (failedDetails.isEmpty()) {
                return true;
            }

            int successCount = 0;
            int totalCount = failedDetails.size();

            // 2. 逐个处理失败记录，基于订单号查找关联记录
            for (CompareBillDetail detail : failedDetails) {
                try {
                    if (retryFailedRefundDetailByOrderNo(detail)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("重试退款记录失败，记录ID：{}，订单号：{}", detail.getId(),
                            detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo(), e);
                }
            }

            // 3. 更新所有相关运营商和版本的对账结果统计
            updateAllRelatedCompareBillResults(failedDetails, ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());

            log.info("退款账单重试完成，成功：{}/{}", successCount, totalCount);
            return successCount == totalCount;

        } catch (Exception e) {
            log.error("重试退款账单对比失败记录时发生异常", e);
            return false;
        }
    }

    /**
     * 获取指定时间段内对比失败的支付记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 失败记录列表
     */
    private List<CompareBillDetail> getFailedPayDetails(LocalDate startDate, LocalDate endDate) {
        // 转换日期格式为yyyyMMdd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String startVersionNo = startDate.format(formatter);
        String endVersionNo = endDate.format(formatter);

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("trans_type", ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode())
                .eq("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode())
                .between("version_no", startVersionNo, endVersionNo)
                .orderBy(true, true, "operator_id", "version_no", "id");

        return compareBillDetailService.list(queryWrapper);
    }

    /**
     * 获取指定时间段内对比失败的退款记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 失败记录列表
     */
    private List<CompareBillDetail> getFailedRefundDetails(LocalDate startDate, LocalDate endDate) {
        // 转换日期格式为yyyyMMdd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String startVersionNo = startDate.format(formatter);
        String endVersionNo = endDate.format(formatter);

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                .eq("trans_type", ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode())
                .eq("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode())
                .between("version_no", startVersionNo, endVersionNo)
                .orderBy(true, true, "operator_id", "version_no", "id");

        return compareBillDetailService.list(queryWrapper);
    }

    /**
     * 根据版本号获取能源平台支付账单
     *
     * @param versionNo 版本号
     * @return 账单列表
     */
    private List<NyptPayBill> getNyptPayBillsByVersion(String versionNo) {
        QueryWrapper<NyptPayBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_no", versionNo);
        return nyptPayBillService.list(queryWrapper);
    }

    /**
     * 根据版本号获取银商支付账单
     *
     * @param versionNo 版本号
     * @return 账单列表
     */
    private List<ChinaumsPayBill> getChinaumsPayBillsByVersion(String versionNo) {
        QueryWrapper<ChinaumsPayBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_no", versionNo);
        return chinaumsPayBillService.list(queryWrapper);
    }

    /**
     * 根据订单号查找能源平台支付账单（不限制版本号）
     *
     * @param businessOrderNo 订单号
     * @return 账单记录
     */
    private NyptPayBill findNyptPayBillByOrderNo(String businessOrderNo) {
        if (StringUtils.isBlank(businessOrderNo)) {
            return null;
        }

        QueryWrapper<NyptPayBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_order_no", businessOrderNo);
        queryWrapper.orderByDesc("create_time"); // 按创建时间倒序，取最新的
        queryWrapper.last("LIMIT 1");

        List<NyptPayBill> bills = nyptPayBillService.list(queryWrapper);
        return bills.isEmpty() ? null : bills.get(0);
    }

    /**
     * 根据订单号查找银商支付账单（不限制版本号）
     *
     * @param businessOrderNo 订单号
     * @return 账单记录
     */
    private ChinaumsPayBill findChinaumsPayBillByOrderNo(String businessOrderNo) {
        if (StringUtils.isBlank(businessOrderNo)) {
            return null;
        }

        QueryWrapper<ChinaumsPayBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_order_no", businessOrderNo);
        queryWrapper.orderByDesc("create_time"); // 按创建时间倒序，取最新的
        queryWrapper.last("LIMIT 1");

        List<ChinaumsPayBill> bills = chinaumsPayBillService.list(queryWrapper);
        return bills.isEmpty() ? null : bills.get(0);
    }

    /**
     * 在明细表中根据订单号查找匹配的记录
     *
     * @param orderNo 订单号
     * @param platform 平台类型（"nypt"表示查找能源平台订单号，"chinaums"表示查找银商订单号）
     * @param excludeId 排除的记录ID（避免查找到自己）
     * @return 匹配的明细记录
     */
    private CompareBillDetail findDetailByOrderNo(String orderNo, String platform, Long excludeId) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();

        // 根据平台类型设置查询条件
        if ("nypt".equals(platform)) {
            queryWrapper.eq("nypt_business_order_no", orderNo);
        } else if ("chinaums".equals(platform)) {
            queryWrapper.eq("chinaums_business_order_no", orderNo);
        } else {
            return null;
        }

        // 排除当前记录
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        // 优先查找已平账的记录，其次查找有完整数据的记录
        queryWrapper.orderByAsc("compare_result") // 0（正常）排在前面
                   .orderByDesc("create_time") // 按创建时间倒序
                   .last("LIMIT 1");

        List<CompareBillDetail> details = compareBillDetailService.list(queryWrapper);
        return details.isEmpty() ? null : details.get(0);
    }

    /**
     * 基于订单号重试单条支付账单对比失败的记录（在明细表内部匹配）
     *
     * @param detail 失败记录
     * @return 重试结果
     */
    private boolean retryFailedPayDetailByOrderNo(CompareBillDetail detail) {
        try {
            boolean updated = false;
            String originalCompareResult = detail.getCompareResult();

            // 保存原始对比结果
            if (detail.getOriginalCompareResult() == null) {
                detail.setOriginalCompareResult(originalCompareResult);
            }

            // 尝试补充缺失的能源平台数据（基于银商订单号在明细表中查找）
            if (StringUtils.isBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                CompareBillDetail matchedDetail = findDetailByOrderNo(detail.getChinaumsBusinessOrderNo(), "chinaums", detail.getId());
                if (matchedDetail != null && StringUtils.isNotBlank(matchedDetail.getNyptBusinessOrderNo())) {
                    // 补充能源平台数据，但保留原有的银商数据
                    detail.setNyptBusinessOrderNo(matchedDetail.getNyptBusinessOrderNo());
                    detail.setNyptTransDate(matchedDetail.getNyptTransDate());
                    detail.setNyptPayAmount(matchedDetail.getNyptPayAmount());
                    // 保留或补充其他字段
                    if (StringUtils.isBlank(detail.getMainOrderCode()) && StringUtils.isNotBlank(matchedDetail.getMainOrderCode())) {
                        detail.setMainOrderCode(matchedDetail.getMainOrderCode());
                    }
                    if (StringUtils.isBlank(detail.getOperatorId()) && StringUtils.isNotBlank(matchedDetail.getOperatorId())) {
                        detail.setOperatorId(matchedDetail.getOperatorId());
                    }
                    if (StringUtils.isBlank(detail.getFundsFrom()) && StringUtils.isNotBlank(matchedDetail.getFundsFrom())) {
                        detail.setFundsFrom(matchedDetail.getFundsFrom());
                    }
                    updated = true;
                    log.info("从明细表补充能源平台数据成功，银商订单号：{}，匹配到能源平台订单号：{}",
                            detail.getChinaumsBusinessOrderNo(), matchedDetail.getNyptBusinessOrderNo());
                }
            }

            // 尝试补充缺失的银商数据（基于能源平台订单号在明细表中查找）
            if (StringUtils.isBlank(detail.getChinaumsBusinessOrderNo()) && StringUtils.isNotBlank(detail.getNyptBusinessOrderNo())) {
                CompareBillDetail matchedDetail = findDetailByOrderNo(detail.getNyptBusinessOrderNo(), "nypt", detail.getId());
                if (matchedDetail != null && StringUtils.isNotBlank(matchedDetail.getChinaumsBusinessOrderNo())) {
                    // 补充银商数据，但保留原有的能源平台数据
                    detail.setChinaumsBusinessOrderNo(matchedDetail.getChinaumsBusinessOrderNo());
                    detail.setChinaumsTransDate(matchedDetail.getChinaumsTransDate());
                    detail.setChinaumsPayAmount(matchedDetail.getChinaumsPayAmount());
                    // 保留或补充其他字段
                    if (StringUtils.isBlank(detail.getMainOrderCode()) && StringUtils.isNotBlank(matchedDetail.getMainOrderCode())) {
                        detail.setMainOrderCode(matchedDetail.getMainOrderCode());
                    }
                    if (StringUtils.isBlank(detail.getOperatorId()) && StringUtils.isNotBlank(matchedDetail.getOperatorId())) {
                        detail.setOperatorId(matchedDetail.getOperatorId());
                    }
                    if (StringUtils.isBlank(detail.getFundsFrom()) && StringUtils.isNotBlank(matchedDetail.getFundsFrom())) {
                        detail.setFundsFrom(matchedDetail.getFundsFrom());
                    }
                    updated = true;
                    log.info("从明细表补充银商数据成功，能源平台订单号：{}，匹配到银商订单号：{}",
                            detail.getNyptBusinessOrderNo(), matchedDetail.getChinaumsBusinessOrderNo());
                }
            }

            // 重新对比金额
            if (StringUtils.isNotBlank(detail.getNyptPayAmount()) && StringUtils.isNotBlank(detail.getChinaumsPayAmount())) {
                try {
                    BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode()); // 平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        updated = true;
                        log.info("金额对比成功，订单号：{}，金额：{}",
                                detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo(),
                                nyptAmount);
                    } else {
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("金额对比失败，订单号：{}，能源平台：{}，银商：{}",
                                detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo(),
                                nyptAmount, chinaumsAmount);
                    }
                } catch (NumberFormatException e) {
                    log.error("金额格式错误，订单号：{}，能源平台：{}，银商：{}",
                            detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo(),
                            detail.getNyptPayAmount(), detail.getChinaumsPayAmount(), e);
                }
            }

            // 更新记录
            if (updated) {
                compareBillDetailService.updateById(detail);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("重试支付记录失败，记录ID：{}", detail.getId(), e);
            return false;
        }
    }

    /**
     * 根据版本号获取能源平台退款账单
     *
     * @param versionNo 版本号
     * @return 账单列表
     */
    private List<NyptRefundBill> getNyptRefundBillsByVersion(String versionNo) {
        QueryWrapper<NyptRefundBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_no", versionNo);
        return nyptRefundBillService.list(queryWrapper);
    }

    /**
     * 根据版本号获取银商退款账单
     *
     * @param versionNo 版本号
     * @return 账单列表
     */
    private List<ChinaumsRefundBill> getChinaumsRefundBillsByVersion(String versionNo) {
        QueryWrapper<ChinaumsRefundBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_no", versionNo);
        return chinaumsRefundBillService.list(queryWrapper);
    }

    /**
     * 根据订单号查找能源平台退款账单（不限制版本号）
     *
     * @param businessOrderNo 订单号
     * @return 账单记录
     */
    private NyptRefundBill findNyptRefundBillByOrderNo(String businessOrderNo) {
        if (StringUtils.isBlank(businessOrderNo)) {
            return null;
        }

        QueryWrapper<NyptRefundBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_order_code", businessOrderNo); // 退款账单通过主订单号关联
        queryWrapper.orderByDesc("create_time"); // 按创建时间倒序，取最新的
        queryWrapper.last("LIMIT 1");

        List<NyptRefundBill> bills = nyptRefundBillService.list(queryWrapper);
        return bills.isEmpty() ? null : bills.get(0);
    }

    /**
     * 根据订单号查找银商退款账单（不限制版本号）
     *
     * @param businessOrderNo 订单号
     * @return 账单记录
     */
    private ChinaumsRefundBill findChinaumsRefundBillByOrderNo(String businessOrderNo) {
        if (StringUtils.isBlank(businessOrderNo)) {
            return null;
        }

        QueryWrapper<ChinaumsRefundBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_order_code", businessOrderNo); // 退款账单通过主订单号关联
        queryWrapper.orderByDesc("create_time"); // 按创建时间倒序，取最新的
        queryWrapper.last("LIMIT 1");

        List<ChinaumsRefundBill> bills = chinaumsRefundBillService.list(queryWrapper);
        return bills.isEmpty() ? null : bills.get(0);
    }

    /**
     * 在明细表中根据退款订单号查找匹配的记录（处理重复订单号情况）
     *
     * @param orderNo 订单号
     * @param platform 平台类型（"nypt"表示查找能源平台订单号，"chinaums"表示查找银商订单号）
     * @param amount 金额（用于精确匹配）
     * @param transDate 交易时间（用于辅助匹配）
     * @param excludeId 排除的记录ID（避免查找到自己）
     * @return 匹配的明细记录
     */
    private CompareBillDetail findRefundDetailByOrderNo(String orderNo, String platform, String amount, String transDate, Long excludeId) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();

        // 根据平台类型设置查询条件
        if ("nypt".equals(platform)) {
            queryWrapper.eq("nypt_business_order_no", orderNo);
        } else if ("chinaums".equals(platform)) {
            queryWrapper.eq("chinaums_business_order_no", orderNo);
        } else {
            return null;
        }

        // 排除当前记录
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        // 限制为退款交易类型
        queryWrapper.eq("trans_type", ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode());

        List<CompareBillDetail> details = compareBillDetailService.list(queryWrapper);

        // 如果只有一条记录，直接返回
        if (details.size() == 1) {
            return details.get(0);
        }

        // 如果有多条记录，需要根据金额和时间进行精确匹配
        if (details.size() > 1 && StringUtils.isNotBlank(amount)) {
            log.info("发现多条相同订单号的退款记录，订单号：{}，记录数：{}，尝试根据金额匹配", orderNo, details.size());

            // 首先尝试金额精确匹配
            for (CompareBillDetail detail : details) {
                String compareAmount = null;
                if ("nypt".equals(platform)) {
                    compareAmount = detail.getChinaumsPayAmount(); // 查找能源平台订单号时，比较银商金额
                } else {
                    compareAmount = detail.getNyptPayAmount(); // 查找银商订单号时，比较能源平台金额
                }

                if (StringUtils.isNotBlank(compareAmount) && amount.equals(compareAmount)) {
                    log.info("根据金额匹配成功，订单号：{}，金额：{}", orderNo, amount);
                    return detail;
                }
            }

            // 如果金额匹配失败，尝试时间辅助匹配
            if (StringUtils.isNotBlank(transDate)) {
                log.info("金额匹配失败，尝试时间辅助匹配，订单号：{}，时间：{}", orderNo, transDate);

                for (CompareBillDetail detail : details) {
                    String compareDate = null;
                    if ("nypt".equals(platform)) {
                        compareDate = detail.getChinaumsTransDate(); // 查找能源平台订单号时，比较银商时间
                    } else {
                        compareDate = detail.getNyptTransDate(); // 查找银商订单号时，比较能源平台时间
                    }

                    if (StringUtils.isNotBlank(compareDate) && isSameDay(transDate, compareDate)) {
                        log.info("根据时间匹配成功，订单号：{}，时间：{}", orderNo, transDate);
                        return detail;
                    }
                }
            }

            // 如果都无法精确匹配，返回第一条已平账的记录，或者创建时间最新的记录
            CompareBillDetail bestMatch = details.stream()
                    .filter(d -> ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode().equals(d.getCompareResult()))
                    .findFirst()
                    .orElse(details.get(0));

            log.warn("无法精确匹配退款记录，订单号：{}，返回最佳匹配记录ID：{}", orderNo, bestMatch.getId());
            return bestMatch;
        }

        return details.isEmpty() ? null : details.get(0);
    }



    /**
     * 基于订单号重试单条退款账单对比失败的记录（在明细表内部匹配，处理重复订单号）
     *
     * @param detail 失败记录
     * @return 重试结果
     */
    private boolean retryFailedRefundDetailByOrderNo(CompareBillDetail detail) {
        try {
            boolean updated = false;
            String originalCompareResult = detail.getCompareResult();

            // 保存原始对比结果
            if (detail.getOriginalCompareResult() == null) {
                detail.setOriginalCompareResult(originalCompareResult);
            }

            // 尝试补充缺失的能源平台退款数据（基于银商退款订单号在明细表中查找）
            if (StringUtils.isBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                CompareBillDetail matchedDetail = findRefundDetailByOrderNo(detail.getChinaumsBusinessOrderNo(), "chinaums",
                        detail.getChinaumsPayAmount(), detail.getChinaumsTransDate(), detail.getId());
                if (matchedDetail != null && StringUtils.isNotBlank(matchedDetail.getNyptBusinessOrderNo())) {
                    // 补充能源平台数据，但保留原有的银商数据
                    detail.setNyptBusinessOrderNo(matchedDetail.getNyptBusinessOrderNo());
                    detail.setNyptTransDate(matchedDetail.getNyptTransDate());
                    detail.setNyptPayAmount(matchedDetail.getNyptPayAmount());
                    // 保留或补充其他字段
                    if (StringUtils.isBlank(detail.getMainOrderCode()) && StringUtils.isNotBlank(matchedDetail.getMainOrderCode())) {
                        detail.setMainOrderCode(matchedDetail.getMainOrderCode());
                    }
                    if (StringUtils.isBlank(detail.getOperatorId()) && StringUtils.isNotBlank(matchedDetail.getOperatorId())) {
                        detail.setOperatorId(matchedDetail.getOperatorId());
                    }
                    if (StringUtils.isBlank(detail.getFundsFrom()) && StringUtils.isNotBlank(matchedDetail.getFundsFrom())) {
                        detail.setFundsFrom(matchedDetail.getFundsFrom());
                    }
                    updated = true;
                    log.info("从明细表补充能源平台退款数据成功，银商退款订单号：{}，金额：{}，匹配到能源平台退款订单号：{}",
                            detail.getChinaumsBusinessOrderNo(), detail.getChinaumsPayAmount(), matchedDetail.getNyptBusinessOrderNo());
                }
            }

            // 尝试补充缺失的银商退款数据（基于能源平台退款订单号在明细表中查找）
            if (StringUtils.isBlank(detail.getChinaumsBusinessOrderNo()) && StringUtils.isNotBlank(detail.getNyptBusinessOrderNo())) {
                CompareBillDetail matchedDetail = findRefundDetailByOrderNo(detail.getNyptBusinessOrderNo(), "nypt",
                        detail.getNyptPayAmount(), detail.getNyptTransDate(), detail.getId());
                if (matchedDetail != null && StringUtils.isNotBlank(matchedDetail.getChinaumsBusinessOrderNo())) {
                    // 补充银商数据，但保留原有的能源平台数据
                    detail.setChinaumsBusinessOrderNo(matchedDetail.getChinaumsBusinessOrderNo());
                    detail.setChinaumsTransDate(matchedDetail.getChinaumsTransDate());
                    detail.setChinaumsPayAmount(matchedDetail.getChinaumsPayAmount());
                    // 保留或补充其他字段
                    if (StringUtils.isBlank(detail.getMainOrderCode()) && StringUtils.isNotBlank(matchedDetail.getMainOrderCode())) {
                        detail.setMainOrderCode(matchedDetail.getMainOrderCode());
                    }
                    if (StringUtils.isBlank(detail.getOperatorId()) && StringUtils.isNotBlank(matchedDetail.getOperatorId())) {
                        detail.setOperatorId(matchedDetail.getOperatorId());
                    }
                    if (StringUtils.isBlank(detail.getFundsFrom()) && StringUtils.isNotBlank(matchedDetail.getFundsFrom())) {
                        detail.setFundsFrom(matchedDetail.getFundsFrom());
                    }
                    updated = true;
                    log.info("从明细表补充银商退款数据成功，能源平台退款订单号：{}，金额：{}，匹配到银商退款订单号：{}",
                            detail.getNyptBusinessOrderNo(), detail.getNyptPayAmount(), matchedDetail.getChinaumsBusinessOrderNo());
                }
            }

            // 重新对比金额
            if (StringUtils.isNotBlank(detail.getNyptPayAmount()) && StringUtils.isNotBlank(detail.getChinaumsPayAmount())) {
                try {
                    BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode()); // 平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        updated = true;
                        log.info("退款金额对比成功，订单号：{}，金额：{}",
                                detail.getMainOrderCode() != null ? detail.getMainOrderCode() :
                                (detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo()),
                                nyptAmount);
                    } else {
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("退款金额对比失败，订单号：{}，能源平台：{}，银商：{}",
                                detail.getMainOrderCode() != null ? detail.getMainOrderCode() :
                                (detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo()),
                                nyptAmount, chinaumsAmount);
                    }
                } catch (NumberFormatException e) {
                    log.error("退款金额格式错误，订单号：{}，能源平台：{}，银商：{}",
                            detail.getMainOrderCode() != null ? detail.getMainOrderCode() :
                            (detail.getChinaumsBusinessOrderNo() != null ? detail.getChinaumsBusinessOrderNo() : detail.getNyptBusinessOrderNo()),
                            detail.getNyptPayAmount(), detail.getChinaumsPayAmount(), e);
                }
            }

            // 更新记录
            if (updated) {
                compareBillDetailService.updateById(detail);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("重试退款记录失败，记录ID：{}", detail.getId(), e);
            return false;
        }
    }

    /**
     * 更新所有相关的对账结果统计
     *
     * @param failedDetails 失败记录列表
     * @param transType     交易类型
     */
    private void updateAllRelatedCompareBillResults(List<CompareBillDetail> failedDetails, String transType) {
        // 按运营商和版本号分组
        Map<String, Map<String, List<CompareBillDetail>>> groupedDetails = failedDetails.stream()
                .collect(Collectors.groupingBy(CompareBillDetail::getOperatorId,
                        Collectors.groupingBy(CompareBillDetail::getVersionNo)));

        // 更新每个运营商和版本的统计
        for (Map.Entry<String, Map<String, List<CompareBillDetail>>> operatorEntry : groupedDetails.entrySet()) {
            String operatorId = operatorEntry.getKey();

            for (Map.Entry<String, List<CompareBillDetail>> versionEntry : operatorEntry.getValue().entrySet()) {
                String versionNo = versionEntry.getKey();

                try {
                    updateCompareBillResultAfterRetry(operatorId, versionNo, transType);
                    log.info("更新对账结果统计成功，运营商：{}，版本：{}，交易类型：{}", operatorId, versionNo, transType);
                } catch (Exception e) {
                    log.error("更新对账结果统计失败，运营商：{}，版本：{}，交易类型：{}", operatorId, versionNo, transType, e);
                }
            }
        }
    }

    /**
     * 重试失败的支付记录
     *
     * @param detail           失败的对账明细
     * @param nyptBillMap      能源平台账单映射
     * @param chinaumsBillMap  银商账单映射
     * @return 重试结果
     */
    private boolean retryFailedPayDetail(CompareBillDetail detail,
                                         Map<String, NyptPayBill> nyptBillMap,
                                         Map<String, ChinaumsPayBill> chinaumsBillMap) {
        try {
            boolean updated = false;
            String originalCompareResult = detail.getCompareResult();

            // 保存原始比对结果
            if (detail.getOriginalCompareResult() == null) {
                detail.setOriginalCompareResult(originalCompareResult);
            }

            // 如果能源平台订单号为空，尝试匹配
            if (StringUtils.isBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                String businessOrderNo = detail.getChinaumsBusinessOrderNo();
                NyptPayBill nyptBill = nyptBillMap.get(businessOrderNo);

                if (nyptBill != null) {
                    // 找到匹配的能源平台记录，更新明细
                    detail.setNyptBusinessOrderNo(nyptBill.getBusinessOrderNo());
                    detail.setNyptTransDate(nyptBill.getTransDate());
                    detail.setNyptPayAmount(nyptBill.getPayAmount());
                    detail.setMainOrderCode(nyptBill.getMainOrderCode());
                    detail.setOperatorId(nyptBill.getOperatorId());
                    detail.setFundsFrom(nyptBill.getFundsFrom());

                    // 对比金额
                    BigDecimal nyptAmount = new BigDecimal(nyptBill.getPayAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        // 金额一致，平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                        log.info("支付重试成功：订单号 {} 金额匹配，已平账", businessOrderNo);
                    } else {
                        // 金额不一致，仍为异常
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("支付重试：订单号 {} 找到匹配记录但金额不一致，能源平台：{}，银商：{}",
                                businessOrderNo, nyptAmount, chinaumsAmount);
                    }
                    updated = true;
                }
            }
            // 如果银商订单号为空，尝试匹配
            else if (StringUtils.isBlank(detail.getChinaumsBusinessOrderNo()) && StringUtils.isNotBlank(detail.getNyptBusinessOrderNo())) {
                String businessOrderNo = detail.getNyptBusinessOrderNo();
                ChinaumsPayBill chinaumsBill = chinaumsBillMap.get(businessOrderNo);

                if (chinaumsBill != null) {
                    // 找到匹配的银商记录，更新明细
                    detail.setChinaumsBusinessOrderNo(chinaumsBill.getBusinessOrderNo());
                    detail.setChinaumsTransDate(chinaumsBill.getTransDate());
                    detail.setChinaumsPayAmount(chinaumsBill.getPayAmount());

                    // 对比金额
                    BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(chinaumsBill.getPayAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        // 金额一致，平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                        log.info("支付重试成功：订单号 {} 金额匹配，已平账", businessOrderNo);
                    } else {
                        // 金额不一致，仍为异常
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("支付重试：订单号 {} 找到匹配记录但金额不一致，能源平台：{}，银商：{}",
                                businessOrderNo, nyptAmount, chinaumsAmount);
                    }
                    updated = true;
                }
            }
            // 如果双方订单号都存在，重新对比金额
            else if (StringUtils.isNotBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                    // 金额一致，平账
                    detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                    detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                    log.info("支付重试成功：订单号 {} 金额重新对比匹配，已平账", detail.getNyptBusinessOrderNo());
                    updated = true;
                }
            }

            // 如果有更新，保存记录
            if (updated) {
                compareBillDetailService.updateById(detail);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("重试支付记录失败，记录ID：{}", detail.getId(), e);
            return false;
        }
    }

    /**
     * 重试失败的退款记录
     *
     * @param detail           失败的对账明细
     * @param nyptBillMap      能源平台账单映射
     * @param chinaumsBillMap  银商账单映射
     * @return 重试结果
     */
    private boolean retryFailedRefundDetail(CompareBillDetail detail,
                                            Map<String, NyptRefundBill> nyptBillMap,
                                            Map<String, ChinaumsRefundBill> chinaumsBillMap) {
        try {
            boolean updated = false;
            String originalCompareResult = detail.getCompareResult();

            // 保存原始比对结果
            if (detail.getOriginalCompareResult() == null) {
                detail.setOriginalCompareResult(originalCompareResult);
            }

            // 如果能源平台退款ID为空，尝试匹配
            if (StringUtils.isBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                String refundId = detail.getChinaumsBusinessOrderNo();
                NyptRefundBill nyptBill = nyptBillMap.get(refundId);

                if (nyptBill != null) {
                    // 找到匹配的能源平台记录，更新明细
                    detail.setNyptBusinessOrderNo(nyptBill.getRefundId());
                    detail.setNyptTransDate(nyptBill.getRefundDate());
                    detail.setNyptPayAmount(nyptBill.getRefundAmount());
                    detail.setMainOrderCode(nyptBill.getMainOrderCode());
                    detail.setOperatorId(nyptBill.getOperatorId());
                    detail.setFundsFrom(nyptBill.getFundsFrom());

                    // 对比金额
                    BigDecimal nyptAmount = new BigDecimal(nyptBill.getRefundAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        // 金额一致，平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                        log.info("退款重试成功：退款ID {} 金额匹配，已平账", refundId);
                    } else {
                        // 金额不一致，仍为异常
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("退款重试：退款ID {} 找到匹配记录但金额不一致，能源平台：{}，银商：{}",
                                refundId, nyptAmount, chinaumsAmount);
                    }
                    updated = true;
                }
            }
            // 如果银商退款ID为空，尝试匹配
            else if (StringUtils.isBlank(detail.getChinaumsBusinessOrderNo()) && StringUtils.isNotBlank(detail.getNyptBusinessOrderNo())) {
                String refundId = detail.getNyptBusinessOrderNo();
                ChinaumsRefundBill chinaumsBill = chinaumsBillMap.get(refundId);

                if (chinaumsBill != null) {
                    // 找到匹配的银商记录，更新明细
                    detail.setChinaumsBusinessOrderNo(chinaumsBill.getRefundId());
                    detail.setChinaumsTransDate(chinaumsBill.getRefundDate());
                    detail.setChinaumsPayAmount(chinaumsBill.getRefundAmount());

                    // 对比金额
                    BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                    BigDecimal chinaumsAmount = new BigDecimal(chinaumsBill.getRefundAmount());

                    if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                        // 金额一致，平账
                        detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                        log.info("退款重试成功：退款ID {} 金额匹配，已平账", refundId);
                    } else {
                        // 金额不一致，仍为异常
                        detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
                        log.warn("退款重试：退款ID {} 找到匹配记录但金额不一致，能源平台：{}，银商：{}",
                                refundId, nyptAmount, chinaumsAmount);
                    }
                    updated = true;
                }
            }
            // 如果双方退款ID都存在，重新对比金额
            else if (StringUtils.isNotBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(detail.getChinaumsBusinessOrderNo())) {
                BigDecimal nyptAmount = new BigDecimal(detail.getNyptPayAmount());
                BigDecimal chinaumsAmount = new BigDecimal(detail.getChinaumsPayAmount());

                if (nyptAmount.compareTo(chinaumsAmount) == 0) {
                    // 金额一致，平账
                    detail.setBalanceStatus(ReconciliationCompareEnum.BALANCE_STATUS.SETTLED_ACCOUNTS.getCode());
                    detail.setCompareResult(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
                    log.info("退款重试成功：退款ID {} 金额重新对比匹配，已平账", detail.getNyptBusinessOrderNo());
                    updated = true;
                }
            }

            // 如果有更新，保存记录
            if (updated) {
                compareBillDetailService.updateById(detail);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("重试退款记录失败，记录ID：{}", detail.getId(), e);
            return false;
        }
    }

    /**
     * 重试后更新对账结果统计
     *
     * @param operatorId 运营商ID
     * @param versionNo  版本号
     * @param transType  交易类型
     */
    private void updateCompareBillResultAfterRetry(String operatorId, String versionNo, String transType) {
        try {
            // 获取对账结果记录
            CompareBillResult compareBillResult = getOrCreateCompareBillResult(operatorId, versionNo, transType);

            // 重新统计异常记录数
            QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode())
                    .eq("trans_type", transType)
                    .eq("operator_id", operatorId)
                    .eq("version_no", versionNo)
                    .eq("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());

            int abnormalCount = (int) compareBillDetailService.count(queryWrapper);
            compareBillResult.setAbnormalCount(abnormalCount);

            // 更新对比状态
            if (abnormalCount == 0) {
                compareBillResult.setCompareStatus(ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode());
            } else {
                compareBillResult.setCompareStatus(ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
            }

            // 保存更新
            saveOrUpdateCompareBillResult(compareBillResult);

            log.info("更新对账结果统计完成，运营商：{}，版本：{}，交易类型：{}，异常记录数：{}",
                    operatorId, versionNo, transType, abnormalCount);

        } catch (Exception e) {
            log.error("更新对账结果统计失败，运营商：{}，版本：{}，交易类型：{}", operatorId, versionNo, transType, e);
        }
    }
}

