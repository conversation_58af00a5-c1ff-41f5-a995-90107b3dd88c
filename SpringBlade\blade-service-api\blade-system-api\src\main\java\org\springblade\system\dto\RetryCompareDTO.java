package org.springblade.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 重试对账参数
 *
 * <AUTHOR>
 */
@Data
public class RetryCompareDTO {

	/**
	 * 开始日期
	 */
	@NotBlank(message = "开始日期不能为空")
	@ApiModelProperty(value = "开始日期", required = true)
	private String startDate;

	/**
	 * 结束日期
	 */
	@NotBlank(message = "结束日期不能为空")
	@ApiModelProperty(value = "结束日期", required = true)
	private String endDate;
	/**
	 * 比对类型
	 */
	@NotBlank(message = "比对类型不能为空")
	@ApiModelProperty(value = "比对类型（1-银商与移动支付平台账单比对；2-能源平台与星云账单比对；3-能源平台与移动支付平台账单比对；4-能源平台与发行ETC账单比对；5-能源平台与银商账单比对）")
	private String compareType;

}
