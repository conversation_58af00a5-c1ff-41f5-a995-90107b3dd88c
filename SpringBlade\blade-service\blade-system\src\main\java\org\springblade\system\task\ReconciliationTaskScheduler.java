package org.springblade.system.task;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.dto.RetryCompareDTO;
import org.springblade.system.service.ICompareBillResultService;
import org.springblade.system.service.IChinaumsBillFileService;
import org.springblade.system.service.IDailyReconciliationService;
import org.springblade.system.service.INyptPayBillService;
import org.springblade.system.service.INyptRefundBillService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 对账任务调度器
 * 负责管理对账任务的调度和执行
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Component
@AllArgsConstructor
public class ReconciliationTaskScheduler {

    private final ICompareBillResultService compareBillResultService;
    private final IChinaumsBillFileService chinaumsBillFileService;
    private final IDailyReconciliationService dailyReconciliationService;
    private final INyptPayBillService nyptPayBillService;
    private final INyptRefundBillService nyptRefundBillService;

    /**
     * 每日凌晨2点执行对账任务
     * 对前一天的交易记录进行对账
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyReconciliation() {
        log.info("开始执行每日对账任务");
        
        try {
            // 1. 生成能源系统前一天的支付和退费对账数据
            Date yesterday = java.sql.Date.valueOf(LocalDate.now().minusDays(1));
            generateNyptBillData(yesterday);
            
            // 2. 处理银商系统提供的对账文件
            processChinaumsBillFile(yesterday);
            
            // 3. 执行对账比对
            executeReconciliation(yesterday);
            
            // 4. 生成日对账结果统计
            dailyReconciliationService.generateDailyResult(yesterday);
            
            log.info("每日对账任务执行完成");
            
        } catch (Exception e) {
            log.error("每日对账任务执行失败", e);
        }
    }

    /**
     * 每日凌晨3点执行对账重试任务
     * 对前一天对账失败的记录进行重试
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void dailyReconciliationRetry() {
        log.info("开始执行每日对账重试任务");
        
        try {
            String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            RetryCompareDTO retryDTO = new RetryCompareDTO();
            retryDTO.setStartDate(yesterday);
            retryDTO.setEndDate(yesterday);
            retryDTO.setCompareType("5"); // 能源平台与银商账单比对
            
            // 重试失败的对账记录
            boolean retryResult = compareBillResultService.retryFailedRecords(retryDTO);
            
            if (retryResult) {
                log.info("每日对账重试任务执行成功");
            } else {
                log.warn("每日对账重试任务执行失败");
            }
            
        } catch (Exception e) {
            log.error("每日对账重试任务执行失败", e);
        }
    }

    /**
     * 生成能源系统对账数据
     */
    private void generateNyptBillData(Date date) {
        log.info("开始生成能源系统对账数据，日期：{}", date);
        
        try {
            // 生成支付对账数据
            boolean payResult = nyptPayBillService.generateReconciliation(date);
            if (!payResult) {
                log.warn("生成能源系统支付对账数据失败，日期：{}", date);
            }
            
            // 生成退费对账数据
            boolean refundResult = nyptRefundBillService.generateReconciliation(date);
            if (!refundResult) {
                log.warn("生成能源系统退费对账数据失败，日期：{}", date);
            }
            
        } catch (Exception e) {
            log.error("生成能源系统对账数据失败", e);
            throw e;
        }
    }

    /**
     * 处理银商系统对账文件
     */
    private void processChinaumsBillFile(Date date) {
        log.info("开始处理银商系统对账文件，日期：{}", date);
        
        try {
            // 处理银商支付对账文件
            chinaumsBillFileService.processPayBillFile(date);
            
            // 处理银商退费对账文件
            chinaumsBillFileService.processRefundBillFile(date);
            
        } catch (Exception e) {
            log.error("处理银商系统对账文件失败", e);
            throw e;
        }
    }

    /**
     * 执行对账比对
     */
    private void executeReconciliation(Date date) {
        log.info("开始执行对账比对，日期：{}", date);
        
        try {
            LocalDate targetDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), targetDate);
            int dateParam = (int) Math.abs(daysBetween);
            
            // 执行支付账单比对
            compareBillResultService.nyptAndChinaumsPayBillCompare(dateParam);
            
            // 执行退费账单比对
            compareBillResultService.nyptAndChinaumsRefundBillCompare(dateParam);
            
        } catch (Exception e) {
            log.error("执行对账比对失败", e);
            throw e;
        }
    }

    /**
     * 手动触发对账任务
     * @param date 对账日期
     * @return 执行结果
     */
    public boolean manualReconciliation(Date date) {
        log.info("手动触发对账任务，日期：{}", date);
        
        try {
            generateNyptBillData(date);
            processChinaumsBillFile(date);
            executeReconciliation(date);
            dailyReconciliationService.generateDailyResult(date);
            
            log.info("手动对账任务执行完成，日期：{}", date);
            return true;
            
        } catch (Exception e) {
            log.error("手动对账任务执行失败，日期：{}", date, e);
            return false;
        }
    }

    /**
     * 批量重试对账任务
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 执行结果
     */
    public boolean batchRetryReconciliation(String startDate, String endDate) {
        log.info("批量重试对账任务，开始日期：{}，结束日期：{}", startDate, endDate);
        
        try {
            RetryCompareDTO retryDTO = new RetryCompareDTO();
            retryDTO.setStartDate(startDate);
            retryDTO.setEndDate(endDate);
            retryDTO.setCompareType("5"); // 能源平台与银商账单比对
            
            return compareBillResultService.retry(retryDTO);
            
        } catch (Exception e) {
            log.error("批量重试对账任务执行失败", e);
            return false;
        }
    }
}
