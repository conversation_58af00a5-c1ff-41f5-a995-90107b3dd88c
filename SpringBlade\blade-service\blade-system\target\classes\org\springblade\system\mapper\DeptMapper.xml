<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deptResultMap" type="org.springblade.system.entity.Dept">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_name" property="deptName"/>
        <result column="full_name" property="fullName"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select
        id, parent_id, dept_name, full_name, sort, remark, is_deleted
    </sql>

    <select id="selectDeptPage" resultMap="deptResultMap">
        select * from blade_dept where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dept_name as title, id as 'value', id as 'key' from blade_dept where is_deleted = 0
        <if test="_parameter!=null">
            and tenant_id = #{_parameter}
        </if>
    </select>

    <select id="lazyTree" resultMap="treeNodeResultMap">
        SELECT
            d.id AS "id",
            d.parent_id AS "parent_id",
            d.dept_name AS "title",
            d.id AS "value",
            d.id AS "key",
            ( SELECT CASE WHEN count(1) > 0 THEN 1 ELSE 0 END FROM blade_dept WHERE parent_id = d.id AND is_deleted = 0 ) AS "has_children"
        FROM blade_dept d
        <where>
            and d.is_deleted = 0
            <if test="param1 != null">
                and d.parent_id = #{param1}
            </if>
            <if test="param2.deptName != null and param2.deptName != ''">
                and d.dept_name like concat(concat('%', #{param2.deptName}),'%')
            </if>
            <if test="param2.fullName != null and param2.fullName != ''">
                and d.full_name like concat(concat('%', #{param2.fullName}),'%')
            </if>
        </where>
    </select>

    <select id="getDeptNames" resultType="java.lang.String">
        SELECT
        dept_name
        FROM
        blade_dept
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>
