package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;

/**
 * 账单比对结果实体
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("compare_bill_result")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CompareBillResult对象", description = "账单比对结果表")
public class CompareBillResult extends BaseEntity {

    /**
     * 银商订单数
     */
    @ApiModelProperty(value = "银商订单数")
    private int chinaumsTotal;
    /**
     * 银商订单总金额
     */
    @ApiModelProperty(value = "银商订单总金额")
    private BigDecimal chinaumsAmount;
    /**
     * 移动平台订单数
     */
    @ApiModelProperty(value = "移动平台订单数")
    private int ydptTotal;
    /**
     * 移动平台订单总金额
     */
    @ApiModelProperty(value = "移动平台订单总金额")
    private BigDecimal ydptAmount;
    /**
     * 能源平台订单数
     */
    @ApiModelProperty(value = "能源平台订单数")
    private int nyptTotal;
    /**
     * 能源平台订单总金额
     */
    @ApiModelProperty(value = "能源平台订单总金额")
    private BigDecimal nyptAmount;
    /**
     * 星云平台订单数
     */
    @ApiModelProperty(value = "星云平台订单数")
    private int nebulaTotal;
    /**
     * 星云平台订单总金额
     */
    @ApiModelProperty(value = "星云平台订单总金额")
    private BigDecimal nebulaAmount;
    /**
     * ETC订单数
     */
    @ApiModelProperty(value = "ETC订单数")
    private int etcTotal;
    /**
     * ETC支付订单总金额
     */
    @ApiModelProperty(value = "ETC支付订单总金额")
    private BigDecimal etcAmount;
    /**
     * 交易类型
     */
    @ApiModelProperty(value = "交易类型（1-支付；2-退款）")
    private String transType;
    /**
     * 比对状态
     */
    @ApiModelProperty(value = "比对状态（0-正常；1-异常）")
    private String compareStatus;
    /**
     * 对账日期
     */
    @ApiModelProperty(value = "对账日期（N-1天格式yyyy-MM-dd）")
    private String chargeDate;
    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;
    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;

    /**
     * 异常记录总数
     */
    @ApiModelProperty(value = "异常记录总数")
    private int abnormalCount;
    /**
     * 比对类型
     */
    @ApiModelProperty(value = "比对类型（1-银商与移动支付平台账单比对；2-能源平台与星云账单比对；3-能源平台与移动支付平台账单比对；4-能源平台与发行ETC账单比对；5-能源平台与银商账单比对）")
    private String compareType;

}