// src/enums/reconciliationCompareEnum.js

export const COMPARE_TYPE = {
  CHINAUMS_YDPT: '1',
  NYPT_NEBULA: '2',
  NYPT_YDPT: '3',
  NYPT_ETC: '4',
  NYPT_CHINAUMS: '5'
}

export const COMPARE_TYPE_OPTIONS = [
  { value: COMPARE_TYPE.CHINAUMS_YDPT, label: '银商与移动平台账单比对' },
  { value: COMPARE_TYPE.NYPT_NEBULA, label: '能源平台与星云账单比对' },
  { value: COMPARE_TYPE.NYPT_YDPT, label: '能源平台与移动平台账单比对' },
  { value: COMPARE_TYPE.NYPT_ETC, label: '能源平台与发行ETC账单比对' },
  { value: COMPARE_TYPE.NYPT_CHINAUMS, label: '能源平台与银商账单比对' }
]

// 获取所有枚举值的数组
export const getCompareTypeCodes = () => 
  COMPARE_TYPE_OPTIONS.map(opt => opt.value)

// 根据code获取对应的中文标签
export const getCompareTypeLabel = (code) => {
  const found = COMPARE_TYPE_OPTIONS.find(opt => opt.value === code)
  return found ? found.label : '未知类型'
}

// 验证code是否有效
export const isValidCompareType = (code) => 
  COMPARE_TYPE_OPTIONS.some(opt => opt.value === code)


/* 使用：

// 获取所有枚举值的数组
export const getCompareTypeCodes = () => 
  COMPARE_TYPE_OPTIONS.map(opt => opt.value)

// 根据code获取对应的中文标签
export const getCompareTypeLabel = (code) => {
  const found = COMPARE_TYPE_OPTIONS.find(opt => opt.value === code)
  return found ? found.label : '未知类型'
}

// 验证code是否有效
export const isValidCompareType = (code) => 
  COMPARE_TYPE_OPTIONS.some(opt => opt.value === code)
```

### 使用示例

1. **在 Vue 组件中使用枚举：**
```vue
<template>
  <el-select v-model="form.compareType" placeholder="请选择对比类型">
    <el-option
      v-for="option in compareTypeOptions"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    />
  </el-select>
</template>

<script>
import { COMPARE_TYPE_OPTIONS } from '@/enums/reconciliationCompareEnum'

export default {
  data() {
    return {
      compareTypeOptions: COMPARE_TYPE_OPTIONS,
      form: {
        compareType: ''
      }
    }
  }
}
</script>
```

2. **验证用户输入：**
```javascript
import { isValidCompareType } from '@/enums/reconciliationCompareEnum'

if (!isValidCompareType(this.compareType)) {
  this.$message.error('无效的对账类型')
  return
}
```

3. **显示类型标签：**
```vue
<template>
  <div>当前选择: {{ compareTypeLabel }}</div>
</template>

<script>
import { getCompareTypeLabel } from '@/enums/reconciliationCompareEnum'

export default {
  computed: {
    compareTypeLabel() {
      return getCompareTypeLabel(this.form.compareType)
    }
  }
}
</script>
```

### 特点说明

1. **多格式导出**：
   - `COMPARE_TYPE`：键值对形式，适合逻辑判断
   - `COMPARE_TYPE_OPTIONS`：数组形式，适合下拉框/表格
2. **实用函数**：
   - `getCompareTypeCodes()` 获取所有 code
   - `getCompareTypeLabel()` 获取类型描述
   - `isValidCompareType()` 验证有效值
 **/
