package org.springblade.refund.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.refund.entity.CompareBillExceptionDetail;

/**
 * 对账明细表视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CompareBillDetailVO对象", description = "对账明细表")
public class CompareBillDetailVO extends CompareBillDetail {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "条件查询 对账开始时间")
    private String chargeDateStart;

    @ApiModelProperty(value = "条件查询 对账结束时间")
    private String chargeDateEnd;

    @ApiModelProperty(value = "账单类型（1-支付；2-退款）")
    private String transType;

    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;

    @ApiModelProperty(value = "比对状态（0-正常；1-异常）")
    private String compareStatus;

    @ApiModelProperty(value = "运营商ID")
    private String operatorId;

}