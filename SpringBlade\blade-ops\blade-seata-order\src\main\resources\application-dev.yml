#服务器端口
server:
  port: 8501

#数据源配置
spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************************************************************
    username: root
    password: root

# seata配置
seata:
  #registry:
  #  type: nacos
  #  nacos:
  #    server-addr: localhost
  #config:
  #  type: nacos
  #  nacos:
  #    server-addr: localhost
  tx-service-group: blade-seata-order-group
  service:
    grouplist:
      default: 127.0.0.1:8091
    vgroup-mapping:
      blade-seata-order-group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
