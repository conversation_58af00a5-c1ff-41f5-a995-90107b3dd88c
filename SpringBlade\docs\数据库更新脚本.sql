-- 对账系统数据库表结构更新脚本
-- 添加银商检索号+流水号唯一索引字段和能源平台退费ID字段

-- 1. 更新银商支付账单表，添加search_no字段
ALTER TABLE `chinaums_pay_bill` 
ADD COLUMN `search_no` varchar(45) NOT NULL COMMENT '唯一索引，值为银商检索号+银商流水号' AFTER `version_no`;

-- 2. 更新银商退费账单表，添加search_no字段
ALTER TABLE `chinaums_refund_bill` 
ADD COLUMN `search_no` varchar(45) NOT NULL COMMENT '唯一索引，值为银商检索号+银商流水号' AFTER `version_no`;

-- 3. 更新对账明细表，添加chinaums_search_no和nypt_refund_id字段
ALTER TABLE `compare_bill_detail` 
ADD COLUMN `chinaums_search_no` varchar(45) DEFAULT NULL COMMENT '银商检索号+银商流水号（唯一索引）' AFTER `chinaums_pay_amount`,
ADD COLUMN `nypt_refund_id` varchar(255) DEFAULT NULL COMMENT '能源平台退费ID' AFTER `nypt_pay_amount`;

-- 4. 创建银商支付账单表的唯一索引
-- 注意：如果表中已有重复数据，需要先清理重复数据
CREATE UNIQUE INDEX `idx_chinaums_pay_unique` ON `chinaums_pay_bill` (`business_order_no`, `search_no`);

-- 5. 创建银商退费账单表的唯一索引
CREATE UNIQUE INDEX `idx_chinaums_refund_unique` ON `chinaums_refund_bill` (`refund_id`, `search_no`);

-- 6. 创建对账明细表的银商唯一索引
CREATE UNIQUE INDEX `idx_compare_detail_chinaums_unique` ON `compare_bill_detail` (`chinaums_business_order_no`, `chinaums_search_no`, `compare_type`, `trans_type`);

-- 7. 创建对账明细表的能源平台唯一索引
CREATE UNIQUE INDEX `idx_compare_detail_nypt_unique` ON `compare_bill_detail` (`nypt_business_order_no`, `nypt_refund_id`, `compare_type`, `trans_type`);

-- 8. 创建对账结果表的唯一索引（避免重复的对账结果记录）
CREATE UNIQUE INDEX `idx_compare_result_unique` ON `compare_bill_result` (`compare_type`, `trans_type`, `operator_id`, `charge_date`);

-- 9. 为提高查询性能，创建一些常用的索引
CREATE INDEX `idx_chinaums_pay_version` ON `chinaums_pay_bill` (`version_no`, `operator_id`);
CREATE INDEX `idx_chinaums_refund_version` ON `chinaums_refund_bill` (`version_no`, `operator_id`);
CREATE INDEX `idx_compare_detail_version` ON `compare_bill_detail` (`version_no`, `compare_type`, `trans_type`);
CREATE INDEX `idx_compare_detail_result` ON `compare_bill_detail` (`compare_result`, `balance_status`);

-- 10. 数据迁移脚本（可选）
-- 如果现有数据中没有search_no，可以使用以下脚本生成临时值
-- 注意：实际使用时应该从银商系统获取真实的检索号和流水号

-- 为现有的银商支付账单生成临时search_no（基于ID和订单号）
-- UPDATE `chinaums_pay_bill` 
-- SET `search_no` = CONCAT('TEMP_', `id`, '_', SUBSTRING(`business_order_no`, -8))
-- WHERE `search_no` IS NULL OR `search_no` = '';

-- 为现有的银商退费账单生成临时search_no（基于ID和退费ID）
-- UPDATE `chinaums_refund_bill` 
-- SET `search_no` = CONCAT('TEMP_', `id`, '_', SUBSTRING(`refund_id`, -8))
-- WHERE `search_no` IS NULL OR `search_no` = '';

-- 为现有的对账明细记录设置nypt_refund_id
-- 对于支付记录，使用订单号作为退费ID
-- UPDATE `compare_bill_detail` 
-- SET `nypt_refund_id` = `nypt_business_order_no`
-- WHERE `trans_type` = '1' AND (`nypt_refund_id` IS NULL OR `nypt_refund_id` = '');

-- 对于退费记录，如果没有退费ID，使用订单号
-- UPDATE `compare_bill_detail` 
-- SET `nypt_refund_id` = COALESCE(`nypt_business_order_no`, `main_order_code`)
-- WHERE `trans_type` = '2' AND (`nypt_refund_id` IS NULL OR `nypt_refund_id` = '');

-- 11. 验证数据完整性的查询语句
-- 检查是否有空的search_no
-- SELECT COUNT(*) as empty_search_no_count FROM `chinaums_pay_bill` WHERE `search_no` IS NULL OR `search_no` = '';
-- SELECT COUNT(*) as empty_search_no_count FROM `chinaums_refund_bill` WHERE `search_no` IS NULL OR `search_no` = '';

-- 检查是否有空的nypt_refund_id
-- SELECT COUNT(*) as empty_refund_id_count FROM `compare_bill_detail` WHERE `nypt_refund_id` IS NULL OR `nypt_refund_id` = '';

-- 检查重复记录
-- SELECT `business_order_no`, `search_no`, COUNT(*) as count 
-- FROM `chinaums_pay_bill` 
-- GROUP BY `business_order_no`, `search_no` 
-- HAVING count > 1;

-- SELECT `refund_id`, `search_no`, COUNT(*) as count 
-- FROM `chinaums_refund_bill` 
-- GROUP BY `refund_id`, `search_no` 
-- HAVING count > 1;
