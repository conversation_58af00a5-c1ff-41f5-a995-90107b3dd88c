package org.springblade.refund.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.refund.entity.CompareBillResult;
import org.springblade.refund.vo.CompareBillResultVO;

/**
 * 对账结果表 服务类
 *
 * <AUTHOR>
 */
public interface ICompareBillResultService extends IService<CompareBillResult> {

    /**
     * 自定义分页
     *
     * @param page
     * @param compareBillResult
     * @return
     */
    IPage<CompareBillResultVO> selectCompareBillResultPage(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult);

    /**
     * 重试对账
     *
     * @param retryCompareDTO 重试参数：开始日期、结束日期、对账类型
     * @return 是否成功
     */
    boolean retry(RetryCompareDTO retryCompareDTO);

    /**
     * 导出对账数据
     *
     * @param compareBillResult 查询条件
     * @return 文件路径
     */
    String exportData(CompareBillResultVO compareBillResult);

}