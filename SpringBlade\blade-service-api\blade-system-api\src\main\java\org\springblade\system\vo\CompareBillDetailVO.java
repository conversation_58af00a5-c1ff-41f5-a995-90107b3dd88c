package org.springblade.system.vo;

import org.springblade.system.entity.CompareBillDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "CompareBillDetailVO对象", description = "账单比对明细参数")
public class CompareBillDetailVO extends CompareBillDetail {

    private static final long serialVersionUID = 1L;

    /**
     * 平账状态
     */
    @ApiModelProperty(value = "查询条件 平账状态（0-未平账；1-已平账）")
    private String balanceStatus;

    /**
     * 对账时间
     */
    @ApiModelProperty(value = "对账时间")
    private String versionNo;

    /**
     * 版本号范围（开始）
     */
    @ApiModelProperty(value = "版本号范围（开始）")
    private String versionNoStart;

    /**
     * 版本号范围（结束）
     */
    @ApiModelProperty(value = "版本号范围（结束）")
    private String versionNoEnd;

}