<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.NyptRefundBillMapper">

    <select id="selectByDate" resultType="org.springblade.system.vo.NyptRefundBillVO">
        select *
        from nypt_refund_bill
        where is_deleted = 0
        and DATE(refund_date) = DATE(#{date})
    </select>

    <select id="selectByOrderNo" resultType="org.springblade.system.vo.NyptRefundBillVO">
        select *
        from nypt_refund_bill
        where is_deleted = 0
        and main_order_code = #{mainOrderCode}
    </select>

</mapper>
