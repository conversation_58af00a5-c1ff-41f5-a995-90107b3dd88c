import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-system/invoice-issuer/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getOrgIds = (salesId) => {
  return request({
    url: '/blade-system/invoice-issuer/org-ids',
    method: 'get',
    params: { salesId }
  })
}

export const associateOrgs = (salesId, orgIds) => {
  return request({
    url: '/blade-system/invoice-issuer/associate-orgs',
    method: 'post',
    data: { salesId, orgIds }
  })
}

