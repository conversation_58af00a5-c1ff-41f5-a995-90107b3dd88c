package org.springblade.system.service;

import java.util.Date;

/**
 * 银商对账文件处理服务接口
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IChinaumsBillFileService {

    /**
     * 处理银商支付对账文件
     * @param date 对账日期
     * @return 处理结果
     */
    boolean processPayBillFile(Date date);

    /**
     * 处理银商退费对账文件
     * @param date 对账日期
     * @return 处理结果
     */
    boolean processRefundBillFile(Date date);

    /**
     * 从指定路径读取银商对账文件
     * @param filePath 文件路径
     * @param date 对账日期
     * @param billType 账单类型（1-支付；2-退费）
     * @return 处理结果
     */
    boolean processFileFromPath(String filePath, Date date, String billType);

    /**
     * 验证对账文件格式
     * @param filePath 文件路径
     * @return 验证结果
     */
    boolean validateFileFormat(String filePath);
}
