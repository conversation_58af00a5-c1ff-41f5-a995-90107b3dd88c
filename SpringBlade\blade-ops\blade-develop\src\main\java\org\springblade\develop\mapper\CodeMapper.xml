<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.develop.mapper.CodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="codeResultMap" type="org.springblade.develop.entity.Code">
        <id column="id" property="id"/>
        <result column="datasource_id" property="datasourceId"/>
        <result column="service_name" property="serviceName"/>
        <result column="code_name" property="codeName"/>
        <result column="table_name" property="tableName"/>
        <result column="pk_name" property="pkName"/>
        <result column="base_mode" property="baseMode"/>
        <result column="wrap_mode" property="wrapMode"/>
        <result column="table_prefix" property="tablePrefix"/>
        <result column="package_name" property="packageName"/>
        <result column="api_path" property="apiPath"/>
        <result column="web_path" property="webPath"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

</mapper>
