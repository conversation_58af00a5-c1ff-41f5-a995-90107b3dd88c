import request from "@/router/axios";

// 获取对账结果列表（包含明细数据）
export const getListWithDetails = (current, size, params) => {
  // 移除chargeDate参数，使用chargeDateStart和chargeDateEnd
  return request({
    url: '/api/charging-station-refund/compare-bill/include_details_page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

// 获取每日对账分页数据（blade-refund）
export const getDailyComparePage = (current, size, params) => {
  return request({
    url: '/api/charging-station-refund/compare-bill/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

// // 获取对账明细列表
// export const getDetail = (current, size, params) => {
//   return request({
//     url: '/api/charging-station-refund/compare-bill-detail/page',
//     method: 'get',
//     params: {
//       ...params,
//       current,
//       size
//     }
//   });
// };

// 重试对账
export const retry = (params) => {
  return request({
    url: '/api/charging-station-refund/compare-bill/retry',
    method: 'post',
    data: params
  });
};

// 导出对账数据
export const exportData = (param) => {
  return request({
    url: "/api/charging-station-refund/compare-bill/export",
    method: "get",
    params: {
      ...param,
    },
    responseType: "blob",
  });
};