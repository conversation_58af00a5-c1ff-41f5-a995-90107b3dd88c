package org.springblade.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.dto.ManualMatchDTO;
import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.enums.ReconciliationCompareEnum;
import org.springblade.system.service.ICompareBillDetailService;
import org.springblade.system.service.IReconciliationExceptionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 对账异常处理服务实现
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReconciliationExceptionServiceImpl implements IReconciliationExceptionService {

    private final ICompareBillDetailService compareBillDetailService;

    @Override
    public List<CompareBillDetail> queryExceptionRecords(String startDate, String endDate, 
                                                         String operatorId, String transType) {
        log.info("查询异常对账记录，开始日期：{}，结束日期：{}，运营商：{}，交易类型：{}", 
                startDate, endDate, operatorId, transType);
        
        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
        
        if (StrUtil.isNotBlank(startDate)) {
            queryWrapper.ge("version_no", startDate.replace("-", ""));
        }
        if (StrUtil.isNotBlank(endDate)) {
            queryWrapper.le("version_no", endDate.replace("-", ""));
        }
        if (StrUtil.isNotBlank(operatorId)) {
            queryWrapper.eq("operator_id", operatorId);
        }
        if (StrUtil.isNotBlank(transType)) {
            queryWrapper.eq("trans_type", transType);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        return compareBillDetailService.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean manualMatch(ManualMatchDTO manualMatchDTO) {
        log.info("开始手动匹配异常记录，异常记录ID：{}，匹配记录ID：{}", 
                manualMatchDTO.getExceptionDetailId(), manualMatchDTO.getMatchDetailId());
        
        try {
            // 获取异常记录
            CompareBillDetail exceptionDetail = compareBillDetailService.getById(manualMatchDTO.getExceptionDetailId());
            if (exceptionDetail == null) {
                log.error("异常记录不存在，ID：{}", manualMatchDTO.getExceptionDetailId());
                return false;
            }
            
            // 获取匹配记录
            CompareBillDetail matchDetail = compareBillDetailService.getById(manualMatchDTO.getMatchDetailId());
            if (matchDetail == null) {
                log.error("匹配记录不存在，ID：{}", manualMatchDTO.getMatchDetailId());
                return false;
            }
            
            // 验证匹配条件
            if (!validateMatchConditions(exceptionDetail, matchDetail, manualMatchDTO.getForceMatch())) {
                log.error("匹配条件验证失败");
                return false;
            }
            
            // 交换数据
            boolean swapResult = swapRecordData(exceptionDetail.getId(), matchDetail.getId());
            if (!swapResult) {
                log.error("交换记录数据失败");
                return false;
            }
            
            // 更新异常记录状态为正常
            UpdateWrapper<CompareBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", exceptionDetail.getId())
                    .set("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode())
                    .set("balance_status", "1")
                    .set("balance_person", manualMatchDTO.getOperatorName())
                    .set("balance_time", new Date())
                    .set("remark", manualMatchDTO.getRemark())
                    .set("update_time", new Date());
            
            boolean updateResult = compareBillDetailService.update(updateWrapper);
            
            log.info("手动匹配完成，结果：{}", updateResult);
            return updateResult;
            
        } catch (Exception e) {
            log.error("手动匹配异常记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean forceMarkAsNormal(Long detailId, String operatorId, String remark) {
        log.info("强制标记为正常，记录ID：{}，操作员：{}", detailId, operatorId);
        
        try {
            UpdateWrapper<CompareBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", detailId)
                    .set("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.NORMAL.getCode())
                    .set("balance_status", "1")
                    .set("balance_person", operatorId)
                    .set("balance_time", new Date())
                    .set("remark", remark)
                    .set("update_time", new Date());
            
            boolean result = compareBillDetailService.update(updateWrapper);
            log.info("强制标记为正常完成，结果：{}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("强制标记为正常失败", e);
            return false;
        }
    }

    @Override
    public List<CompareBillDetail> findMatchableRecords(String orderNo, String amount, 
                                                        String platform, String transType) {
        log.info("查找可匹配记录，订单号：{}，金额：{}，平台：{}，交易类型：{}", 
                orderNo, amount, platform, transType);
        
        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trans_type", transType);
        
        // 根据平台查找对应的订单号和金额
        if ("nypt".equals(platform)) {
            // 查找能源平台的记录，匹配银商的订单号和金额
            queryWrapper.and(wrapper -> wrapper
                    .eq("chinaums_business_order_no", orderNo)
                    .eq("chinaums_pay_amount", amount));
        } else if ("chinaums".equals(platform)) {
            // 查找银商的记录，匹配能源平台的订单号和金额
            queryWrapper.and(wrapper -> wrapper
                    .eq("nypt_business_order_no", orderNo)
                    .eq("nypt_pay_amount", amount));
        }
        
        // 只查找异常记录
        queryWrapper.eq("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode());
        queryWrapper.orderByDesc("create_time");
        
        return compareBillDetailService.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean swapRecordData(Long sourceDetailId, Long targetDetailId) {
        log.info("交换记录数据，源记录ID：{}，目标记录ID：{}", sourceDetailId, targetDetailId);
        
        try {
            CompareBillDetail sourceDetail = compareBillDetailService.getById(sourceDetailId);
            CompareBillDetail targetDetail = compareBillDetailService.getById(targetDetailId);
            
            if (sourceDetail == null || targetDetail == null) {
                log.error("记录不存在，源记录ID：{}，目标记录ID：{}", sourceDetailId, targetDetailId);
                return false;
            }
            
            // 交换能源平台数据
            String tempNyptOrderNo = sourceDetail.getNyptBusinessOrderNo();
            String tempNyptTransDate = sourceDetail.getNyptTransDate();
            String tempNyptAmount = sourceDetail.getNyptPayAmount();
            
            sourceDetail.setNyptBusinessOrderNo(targetDetail.getNyptBusinessOrderNo());
            sourceDetail.setNyptTransDate(targetDetail.getNyptTransDate());
            sourceDetail.setNyptPayAmount(targetDetail.getNyptPayAmount());
            
            targetDetail.setNyptBusinessOrderNo(tempNyptOrderNo);
            targetDetail.setNyptTransDate(tempNyptTransDate);
            targetDetail.setNyptPayAmount(tempNyptAmount);
            
            // 交换银商数据
            String tempChinaumsOrderNo = sourceDetail.getChinaumsBusinessOrderNo();
            String tempChinaumsTransDate = sourceDetail.getChinaumsTransDate();
            String tempChinaumsAmount = sourceDetail.getChinaumsPayAmount();
            
            sourceDetail.setChinaumsBusinessOrderNo(targetDetail.getChinaumsBusinessOrderNo());
            sourceDetail.setChinaumsTransDate(targetDetail.getChinaumsTransDate());
            sourceDetail.setChinaumsPayAmount(targetDetail.getChinaumsPayAmount());
            
            targetDetail.setChinaumsBusinessOrderNo(tempChinaumsOrderNo);
            targetDetail.setChinaumsTransDate(tempChinaumsTransDate);
            targetDetail.setChinaumsPayAmount(tempChinaumsAmount);
            
            // 更新记录
            sourceDetail.setUpdateTime(new Date());
            targetDetail.setUpdateTime(new Date());
            
            boolean result = compareBillDetailService.updateById(sourceDetail) && 
                           compareBillDetailService.updateById(targetDetail);
            
            log.info("交换记录数据完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("交换记录数据失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchProcessExceptions(List<Long> detailIds, String action, String operatorId) {
        log.info("批量处理异常记录，记录数量：{}，处理动作：{}，操作员：{}", 
                detailIds.size(), action, operatorId);
        
        if (CollectionUtil.isEmpty(detailIds)) {
            return true;
        }
        
        try {
            boolean allSuccess = true;
            
            for (Long detailId : detailIds) {
                boolean result = false;
                
                switch (action) {
                    case "ignore":
                        result = forceMarkAsNormal(detailId, operatorId, "批量忽略处理");
                        break;
                    case "retry":
                        // 重置为未处理状态，等待重新对账
                        result = resetRecordStatus(detailId, operatorId);
                        break;
                    default:
                        log.warn("不支持的处理动作：{}", action);
                        result = false;
                }
                
                if (!result) {
                    allSuccess = false;
                    log.error("处理记录失败，ID：{}，动作：{}", detailId, action);
                }
            }
            
            log.info("批量处理异常记录完成，总体结果：{}", allSuccess);
            return allSuccess;
            
        } catch (Exception e) {
            log.error("批量处理异常记录失败", e);
            return false;
        }
    }

    /**
     * 验证匹配条件
     */
    private boolean validateMatchConditions(CompareBillDetail exceptionDetail, 
                                          CompareBillDetail matchDetail, Boolean forceMatch) {
        // 检查订单号是否相同
        if (!StrUtil.equals(exceptionDetail.getMainOrderCode(), matchDetail.getMainOrderCode())) {
            log.error("订单号不匹配，异常记录：{}，匹配记录：{}", 
                    exceptionDetail.getMainOrderCode(), matchDetail.getMainOrderCode());
            return false;
        }
        
        // 如果不是强制匹配，检查金额是否相同
        if (!Boolean.TRUE.equals(forceMatch)) {
            String exceptionAmount = getRecordAmount(exceptionDetail);
            String matchAmount = getRecordAmount(matchDetail);
            
            if (!StrUtil.equals(exceptionAmount, matchAmount)) {
                log.error("金额不匹配，异常记录：{}，匹配记录：{}", exceptionAmount, matchAmount);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取记录金额
     */
    private String getRecordAmount(CompareBillDetail detail) {
        if (StrUtil.isNotBlank(detail.getNyptPayAmount())) {
            return detail.getNyptPayAmount();
        } else if (StrUtil.isNotBlank(detail.getChinaumsPayAmount())) {
            return detail.getChinaumsPayAmount();
        }
        return "0";
    }

    /**
     * 重置记录状态
     */
    private boolean resetRecordStatus(Long detailId, String operatorId) {
        try {
            UpdateWrapper<CompareBillDetail> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", detailId)
                    .set("compare_result", ReconciliationCompareEnum.COMPARE_RESULT.UNNORMAL.getCode())
                    .set("balance_status", "0")
                    .set("balance_person", null)
                    .set("balance_time", null)
                    .set("remark", "重置状态，等待重新对账")
                    .set("update_time", new Date());
            
            return compareBillDetailService.update(updateWrapper);
            
        } catch (Exception e) {
            log.error("重置记录状态失败，ID：{}", detailId, e);
            return false;
        }
    }
}
