package org.springblade.refund.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.refund.entity.CompareBillDetail;
import org.springblade.refund.vo.CompareBillDetailVO;

import java.util.List;

/**
 * 对账明细表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface CompareBillDetailMapper extends BaseMapper<CompareBillDetail> {

    /**
     * 自定义分页
     *
     * @param page
     * @param compareBillDetail
     * @return
     */
    List<CompareBillDetailVO> selectCompareBillDetailPage(IPage page, CompareBillDetailVO compareBillDetail);

}