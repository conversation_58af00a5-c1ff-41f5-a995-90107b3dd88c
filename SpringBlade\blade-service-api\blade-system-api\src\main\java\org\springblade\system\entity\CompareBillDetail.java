package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 账单比对明细实体
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("compare_bill_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CompareBillDetail对象", description = "账单比对明细表")
public class CompareBillDetail extends BaseEntity {

    /**
     * 充电主订单号
     */
    @ApiModelProperty(value = "充电订单号")
    private String mainOrderCode;
    /**
     * 银商支付订单号
     */
    @ApiModelProperty(value = "银商交易订单号")
    private String chinaumsBusinessOrderNo;
    /**
     * 银商交易时间
     */
    @ApiModelProperty(value = "银商交易时间")
    private String chinaumsTransDate;
    /**
     * 银商支付金额
     */
    @ApiModelProperty(value = "银商交易金额")
    private String chinaumsPayAmount;
    /**
     * 银商检索号+银商流水号（唯一索引）
     */
    @ApiModelProperty(value = "银商检索号+银商流水号（唯一索引）")
    private String chinaumsSearchNo;
    /**
     * 移动平台支付订单号
     */
    @ApiModelProperty(value = "移动平台交易订单号")
    private String ydptBusinessOrderNo;
    /**
     * 移动平台交易时间
     */
    @ApiModelProperty(value = "移动平台交易时间")
    private String ydptTransDate;
    /**
     * 移动平台支付金额
     */
    @ApiModelProperty(value = "移动平台交易金额")
    private String ydptPayAmount;
    /**
     * 能源平台支付订单号
     */
    @ApiModelProperty(value = "能源平台交易订单号")
    private String nyptBusinessOrderNo;
    /**
     * 能源平台交易时间
     */
    @ApiModelProperty(value = "能源平台交易时间")
    private String nyptTransDate;
    /**
     * 能源平台支付金额
     */
    @ApiModelProperty(value = "能源平台交易金额")
    private String nyptPayAmount;
    /**
     * 能源平台退费ID
     */
    @ApiModelProperty(value = "能源平台退费ID")
    private String nyptRefundId;
    /**
     * 星云支付订单号
     */
    @ApiModelProperty(value = "星云交易订单号")
    private String nebulaBusinessOrderNo;
    /**
     * 星云交易时间
     */
    @ApiModelProperty(value = "星云交易时间")
    private String nebulaTransDate;
    /**
     * 星云支付金额
     */
    @ApiModelProperty(value = "星云交易金额")
    private String nebulaPayAmount;
    /**
     * etc支付订单号
     */
    @ApiModelProperty(value = "etc交易订单号")
    private String etcBusinessOrderNo;
    /**
     * etc交易时间
     */
    @ApiModelProperty(value = "etc交易时间")
    private String etcTransDate;
    /**
     * etc支付金额
     */
    @ApiModelProperty(value = "etc交易金额")
    private String etcPayAmount;
    /**
     * 平账状态
     */
    @ApiModelProperty(value = "平账状态（0-未平账；1-已平账）")
    private String balanceStatus;
    /**
     * 平账人员
     */
    @ApiModelProperty(value = "平账人员")
    private String balancePerson;
    /**
     * 平账时间
     */
    @ApiModelProperty(value = "平账时间")
    private Date balanceTime;
    /**
     * 交易类型
     */
    @ApiModelProperty(value = "交易类型（1-支付；2-退款）")
    private String transType;
    /**
     * 比对类型
     */
    @ApiModelProperty(value = "比对类型（1-银商与移动支付平台账单比对；2-能源平台与星云账单比对；3-能源平台与移动支付平台账单比对；4-能源平台与发行ETC账单比对；5-能源平台与银商账单比对）")
    private String compareType;
    /**
     * 比对结果
     */
    @ApiModelProperty(value = "比对结果（0-正常；1-异常）")
    private String compareResult;
    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;
    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号（N-1天格式yyyyMMdd）")
    private String versionNo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 原始比对结果，用于
     */
    @ApiModelProperty(value = "原始比对结果")
    private String originalCompareResult;
    /**
     * 原始能源平台金额（用于金额调整）
     */
    @ApiModelProperty(value = "原始能源平台金额")
    private String originalNyptPayAmount;
    /**
     * 原始银商金额（用于金额调整）
     */
    @ApiModelProperty(value = "原始银商金额")
    private String originalChinaumsPayAmount;

}