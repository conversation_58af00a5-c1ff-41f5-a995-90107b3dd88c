package org.springblade.refund.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.refund.entity.CompareBillDetail;
import org.springblade.refund.service.ICompareBillDetailService;
import org.springblade.refund.vo.CompareBillDetailVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对账明细表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/compare-bill-detail")
@Api(value = "对账明细表", tags = "对账明细表接口")
public class CompareBillDetailController extends BladeController {

    private final ICompareBillDetailService compareBillDetailService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入compareBillDetail")
    public R<CompareBillDetail> detail(CompareBillDetail compareBillDetail) {
        CompareBillDetail detail = compareBillDetailService.getOne(Condition.getQueryWrapper(compareBillDetail));
        return R.data(detail);
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页", notes = "传入compareBillDetail")
    public R<IPage<CompareBillDetailVO>> page(CompareBillDetailVO compareBillDetail, Query query) {
        IPage<CompareBillDetailVO> pages = compareBillDetailService.selectCompareBillDetailPage(Condition.getPage(query), compareBillDetail);
        return R.data(pages);
    }

}