package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;

@Data
@TableName("invoice_issuer")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InvoiceIssuer对象", description = "开票方信息表")
public class InvoiceIssuer extends BaseEntity {
private static final long serialVersionUID = 1L;
private String salesId;
private String salesCode;
}
