<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.CompareBillDetailMapper">

    <select id="selectCompareBillDetailPage" resultType="org.springblade.system.vo.CompareBillDetailVO">
        select * from compare_bill_detail where is_deleted = 0
        <if test="compareBillDetail.operatorId != null and compareBillDetail.operatorId != ''">
            and operator_id = #{compareBillDetail.operatorId}
        </if>
        <if test="compareBillDetail.fundsFrom != null">
            and funds_from = #{compareBillDetail.fundsFrom}
        </if>
        <if test="compareBillDetail.versionNo != null and compareBillDetail.versionNo != ''">
            and version_no = #{compareBillDetail.versionNo}
        </if>
        <if test="compareBillDetail.versionNoStart != null and compareBillDetail.versionNoStart != ''">
            and version_no &gt;= #{compareBillDetail.versionNoStart}
        </if>
        <if test="compareBillDetail.versionNoEnd != null and compareBillDetail.versionNoEnd != ''">
            and version_no &lt;= #{compareBillDetail.versionNoEnd}
        </if>
        <if test="compareBillDetail.compareResult != null and compareBillDetail.compareResult != ''">
            and compare_result = #{compareBillDetail.compareResult}
        </if>
        <if test="compareBillDetail.transType != null and compareBillDetail.transType != ''">
            and trans_type = #{compareBillDetail.transType}
        </if>
        <if test="compareBillDetail.mainOrderCode != null and compareBillDetail.mainOrderCode != ''">
            and main_order_code like concat('%', #{compareBillDetail.mainOrderCode}, '%')
        </if>
        <if test="compareBillDetail.nyptBusinessOrderNo != null and compareBillDetail.nyptBusinessOrderNo != ''">
            and (nypt_business_order_no like concat('%', #{compareBillDetail.nyptBusinessOrderNo}, '%')
            or chinaums_business_order_no like concat('%', #{compareBillDetail.nyptBusinessOrderNo}, '%'))
        </if>
<!--        <if test="compareBillDetail.chinaumsBusinessOrderNo != null and compareBillDetail.chinaumsBusinessOrderNo != ''">-->
<!--            and chinaums_business_order_no like concat('%', #{compareBillDetail.chinaumsBusinessOrderNo}, '%')-->
<!--        </if>-->
        order by version_no desc, main_order_code, create_time
    </select>

</mapper>
