package org.springblade.refund.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 对账明细表实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("compare_bill_detail")
@ApiModel(value = "CompareBillDetail对象", description = "对账明细表")
public class CompareBillDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 充电订单号
     */
    @ApiModelProperty(value = "充电订单号")
    private String mainOrderCode;

    /**
     * 银商交易订单号
     */
    @ApiModelProperty(value = "银商交易订单号")
    private String chinaumsBusinessOrderNo;

    /**
     * 银商交易时间
     */
    @ApiModelProperty(value = "银商交易时间")
    private String chinaumsTransDate;

    /**
     * 银商交易金额
     */
    @ApiModelProperty(value = "银商交易金额")
    private String chinaumsPayAmount;

    /**
     * 移动平台交易订单号
     */
    @ApiModelProperty(value = "移动平台交易订单号")
    private String ydptBusinessOrderNo;

    /**
     * 移动平台交易时间
     */
    @ApiModelProperty(value = "移动平台交易时间")
    private String ydptTransDate;

    /**
     * 移动平台交易金额
     */
    @ApiModelProperty(value = "移动平台交易金额")
    private String ydptPayAmount;

    /**
     * 能源平台交易订单号
     */
    @ApiModelProperty(value = "能源平台交易订单号")
    private String nyptBusinessOrderNo;

    /**
     * 能源平台交易时间
     */
    @ApiModelProperty(value = "能源平台交易时间")
    private String nyptTransDate;

    /**
     * 能源平台交易金额
     */
    @ApiModelProperty(value = "能源平台交易金额")
    private String nyptPayAmount;

    /**
     * 星云交易订单号
     */
    @ApiModelProperty(value = "星云交易订单号")
    private String nebulaBusinessOrderNo;

    /**
     * 星云平台交易时间
     */
    @ApiModelProperty(value = "星云平台交易时间")
    private String nebulaTransDate;

    /**
     * 星云平台交易金额
     */
    @ApiModelProperty(value = "星云平台交易金额")
    private String nebulaPayAmount;

    /**
     * ETC交易订单号
     */
    @ApiModelProperty(value = "ETC交易订单号")
    private String etcBusinessOrderNo;

    /**
     * ETC交易时间
     */
    @ApiModelProperty(value = "ETC交易时间")
    private String etcTransDate;

    /**
     * ETC交易金额
     */
    @ApiModelProperty(value = "ETC交易金额")
    private String etcPayAmount;

    /**
     * 平账状态（0-未平账；1-已平账）
     */
    @ApiModelProperty(value = "平账状态（0-未平账；1-已平账）")
    private String balanceStatus;

    /**
     * 平账人员
     */
    @ApiModelProperty(value = "平账人员")
    private String balancePerson;

    /**
     * 平账时间
     */
    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
    @ApiModelProperty(value = "平账时间")
    private Date balanceTime;

    /**
     * 交易类型（1-支付；2-退款）
     */
    @ApiModelProperty(value = "交易类型（1-支付；2-退款）")
    private String transType;

    /**
     * 比对类型（1-银商与移动支付平台支付账单比对；2-能源平台与星云支付账单比对；3-能源平台与移动支付平台支付账单比对；4-能源平台与发行ETC支付账单比对）
     */
    @ApiModelProperty(value = "比对类型（1-银商与移动支付平台支付账单比对；2-能源平台与星云支付账单比对；3-能源平台与移动支付平台支付账单比对；4-能源平台与发行ETC支付账单比对）")
    private String compareType;

    /**
     * 比对结果（0-正常；1-异常）
     */
    @ApiModelProperty(value = "比对结果（0-正常；1-异常）")
    private String compareResult;

    /**
     * 版本号（N-1天格式yyyyMMdd）
     */
    @ApiModelProperty(value = "版本号（N-1天格式yyyyMMdd）")
    private String versionNo;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private Integer fundsFrom;

    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "创建人")
    private Long createUser;

    /**
     * 创建部门
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "更新人")
    private Long updateUser;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 是否已删除
     */
    @TableLogic
    @ApiModelProperty(value = "是否已删除")
    private Integer isDeleted;

}