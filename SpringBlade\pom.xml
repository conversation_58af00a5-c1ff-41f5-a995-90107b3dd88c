<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.springblade</groupId>
    <artifactId>SpringBlade</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <revision>4.5.0</revision>

        <blade.tool.version>4.5.0</blade.tool.version>

        <java.version>17</java.version>
        <maven.plugin.version>3.11.0</maven.plugin.version>
        <maven.flatten.version>1.3.0</maven.flatten.version>

        <captcha.version>1.6.2</captcha.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <poi.version>4.1.2</poi.version>
        <mica.auto.version>3.1.3</mica.auto.version>

        <!-- 推荐使用Harbor -->
        <docker.registry.url>***********</docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.plugin.version>1.2.0</docker.plugin.version>
    </properties>

    <modules>
        <module>blade-auth</module>
        <module>blade-gateway</module>
        <module>blade-ops</module>
        <module>blade-service</module>
        <module>blade-service-api</module>
        <module>blade-common</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- blade bom -->
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-core-bom</artifactId>
                <version>${blade.tool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- blade api -->
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-dict-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-scope-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-desk-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-user-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-system-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.springblade</groupId>
                <artifactId>blade-demo-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- SpringAdmin -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>3.2.3</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>3.2.3</version>
            </dependency>
            <!-- Captcha -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${captcha.version}</version>
            </dependency>
            <!-- Easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>ooxml-schemas</artifactId>
                <version>1.4</version>
            </dependency>
            <!-- dynamic-datasource -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>4.3.0</version>
            </dependency>
            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>0.11.2</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>0.11.2</version>
            </dependency>
            <!-- Mica -->
            <dependency>
                <groupId>net.dreamlu</groupId>
                <artifactId>mica-auto</artifactId>
                <version>${mica.auto.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>3.2.10</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.1.0</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <target>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          todir="${project.root.directory}/target"
                                          file="${project.build.directory}/${project.artifactId}.jar"/>
                                </target>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${maven.flatten.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                    <pomElements>
                        <url>remove</url>
                        <scm>remove</scm>
                        <developers>remove</developers>
                        <properties>remove</properties>
                        <repositories>remove</repositories>
                        <distributionManagement>remove</distributionManagement>
                    </pomElements>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <id>directories</id>
                        <goals>
                            <goal>highest-basedir</goal>
                        </goals>
                        <phase>initialize</phase>
                        <configuration>
                            <property>project.root.directory</property>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>aliyun-repos</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
