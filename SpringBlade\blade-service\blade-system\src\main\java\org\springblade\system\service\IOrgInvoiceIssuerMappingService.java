package org.springblade.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.system.entity.OrgInvoiceIssuerMapping;

import java.util.List;

/**
 * 机构-开票方 关联 服务类
 */
public interface IOrgInvoiceIssuerMappingService extends IService<OrgInvoiceIssuerMapping> {
    boolean saveMappings(String salesId, List<Long> orgIds);
    List<Long> getOrgIdsBySalesId(String salesId);
}

