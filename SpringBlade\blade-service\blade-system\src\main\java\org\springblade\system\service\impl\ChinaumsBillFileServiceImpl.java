package org.springblade.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.entity.ChinaumsPayBill;
import org.springblade.system.entity.ChinaumsRefundBill;
import org.springblade.system.service.IChinaumsBillFileService;
import org.springblade.system.service.IChinaumsPayBillService;
import org.springblade.system.service.IChinaumsRefundBillService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 银商对账文件处理服务实现
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class ChinaumsBillFileServiceImpl implements IChinaumsBillFileService {

    private final IChinaumsPayBillService chinaumsPayBillService;
    private final IChinaumsRefundBillService chinaumsRefundBillService;

    @Value("${reconciliation.chinaums.file.path:/data/reconciliation/chinaums/}")
    private String chinaumsFilePath;

    @Value("${reconciliation.chinaums.file.encoding:UTF-8}")
    private String fileEncoding;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processPayBillFile(Date date) {
        log.info("开始处理银商支付对账文件，日期：{}", DateUtil.formatDate(date));
        
        try {
            String dateStr = DateUtil.format(date, "yyyyMMdd");
            String fileName = "chinaums_pay_" + dateStr + ".txt";
            String filePath = chinaumsFilePath + fileName;
            
            // 检查文件是否存在
            if (!FileUtil.exist(filePath)) {
                log.warn("银商支付对账文件不存在：{}", filePath);
                return false;
            }
            
            return processFileFromPath(filePath, date, "1");
            
        } catch (Exception e) {
            log.error("处理银商支付对账文件失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processRefundBillFile(Date date) {
        log.info("开始处理银商退费对账文件，日期：{}", DateUtil.formatDate(date));
        
        try {
            String dateStr = DateUtil.format(date, "yyyyMMdd");
            String fileName = "chinaums_refund_" + dateStr + ".txt";
            String filePath = chinaumsFilePath + fileName;
            
            // 检查文件是否存在
            if (!FileUtil.exist(filePath)) {
                log.warn("银商退费对账文件不存在：{}", filePath);
                return false;
            }
            
            return processFileFromPath(filePath, date, "2");
            
        } catch (Exception e) {
            log.error("处理银商退费对账文件失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processFileFromPath(String filePath, Date date, String billType) {
        log.info("开始处理银商对账文件，文件路径：{}，账单类型：{}", filePath, billType);
        
        try {
            // 验证文件格式
            if (!validateFileFormat(filePath)) {
                log.error("银商对账文件格式验证失败：{}", filePath);
                return false;
            }
            
            // 读取文件内容
            List<String> lines = FileUtil.readLines(new File(filePath), StandardCharsets.UTF_8);
            if (CollectionUtil.isEmpty(lines)) {
                log.warn("银商对账文件为空：{}", filePath);
                return true; // 空文件视为处理成功
            }
            
            String versionNo = DateUtil.format(date, "yyyyMMdd");
            
            if ("1".equals(billType)) {
                // 处理支付账单
                return processPayBillLines(lines, versionNo);
            } else if ("2".equals(billType)) {
                // 处理退费账单
                return processRefundBillLines(lines, versionNo);
            } else {
                log.error("不支持的账单类型：{}", billType);
                return false;
            }
            
        } catch (Exception e) {
            log.error("处理银商对账文件失败，文件路径：{}", filePath, e);
            return false;
        }
    }

    @Override
    public boolean validateFileFormat(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                return false;
            }
            
            // 检查文件扩展名
            String fileName = file.getName();
            if (!fileName.endsWith(".txt") && !fileName.endsWith(".csv")) {
                log.warn("不支持的文件格式：{}", fileName);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证文件格式失败：{}", filePath, e);
            return false;
        }
    }

    /**
     * 处理支付账单行数据
     */
    private boolean processPayBillLines(List<String> lines, String versionNo) {
        log.info("开始处理银商支付账单数据，行数：{}", lines.size());
        
        try {
            List<ChinaumsPayBill> billsToSave = new ArrayList<>();
            
            // 跳过标题行（如果有）
            int startIndex = isHeaderLine(lines.get(0)) ? 1 : 0;
            
            for (int i = startIndex; i < lines.size(); i++) {
                String line = lines.get(i);
                if (StrUtil.isBlank(line)) {
                    continue;
                }
                
                ChinaumsPayBill bill = parsePayBillLine(line, versionNo);
                if (bill != null) {
                    billsToSave.add(bill);
                }
            }
            
            if (CollectionUtil.isNotEmpty(billsToSave)) {
                // 批量保存或更新
                return saveChinaumsPayBills(billsToSave, versionNo);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("处理银商支付账单数据失败", e);
            return false;
        }
    }

    /**
     * 处理退费账单行数据
     */
    private boolean processRefundBillLines(List<String> lines, String versionNo) {
        log.info("开始处理银商退费账单数据，行数：{}", lines.size());
        
        try {
            List<ChinaumsRefundBill> billsToSave = new ArrayList<>();
            
            // 跳过标题行（如果有）
            int startIndex = isHeaderLine(lines.get(0)) ? 1 : 0;
            
            for (int i = startIndex; i < lines.size(); i++) {
                String line = lines.get(i);
                if (StrUtil.isBlank(line)) {
                    continue;
                }
                
                ChinaumsRefundBill bill = parseRefundBillLine(line, versionNo);
                if (bill != null) {
                    billsToSave.add(bill);
                }
            }
            
            if (CollectionUtil.isNotEmpty(billsToSave)) {
                // 批量保存或更新
                return saveChinaumsRefundBills(billsToSave, versionNo);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("处理银商退费账单数据失败", e);
            return false;
        }
    }

    /**
     * 判断是否为标题行
     */
    private boolean isHeaderLine(String line) {
        if (StrUtil.isBlank(line)) {
            return false;
        }
        
        // 简单判断：如果包含中文字符或常见标题关键字，认为是标题行
        return line.contains("订单号") || line.contains("金额") || line.contains("时间") || 
               line.contains("order") || line.contains("amount") || line.contains("date");
    }

    /**
     * 解析支付账单行数据
     * 格式：主订单号|支付订单号|交易时间|支付金额|运营商ID|银商检索号|银商流水号
     */
    private ChinaumsPayBill parsePayBillLine(String line, String versionNo) {
        try {
            String[] fields = line.split("\\|");
            if (fields.length < 7) {
                log.warn("银商支付账单行格式不正确，期望7个字段，实际{}个：{}", fields.length, line);
                return null;
            }

            ChinaumsPayBill bill = new ChinaumsPayBill();
            bill.setMainOrderCode(fields[0].trim());
            bill.setBusinessOrderNo(fields[1].trim());
            bill.setTransDate(fields[2].trim());
            bill.setPayAmount(fields[3].trim());
            bill.setOperatorId(fields[4].trim());

            // 组合银商检索号+银商流水号作为唯一索引
            String searchNo = fields[5].trim() + fields[6].trim();
            bill.setSearchNo(searchNo);

            bill.setVersionNo(versionNo);

            return bill;

        } catch (Exception e) {
            log.error("解析银商支付账单行失败：{}", line, e);
            return null;
        }
    }

    /**
     * 解析退费账单行数据
     * 格式：主订单号|退费ID|退款时间|退款金额|运营商ID|银商检索号|银商流水号
     */
    private ChinaumsRefundBill parseRefundBillLine(String line, String versionNo) {
        try {
            String[] fields = line.split("\\|");
            if (fields.length < 7) {
                log.warn("银商退费账单行格式不正确，期望7个字段，实际{}个：{}", fields.length, line);
                return null;
            }

            ChinaumsRefundBill bill = new ChinaumsRefundBill();
            bill.setMainOrderCode(fields[0].trim());
            bill.setRefundId(fields[1].trim());
            bill.setRefundDate(fields[2].trim());
            bill.setRefundAmount(fields[3].trim());
            bill.setOperatorId(fields[4].trim());

            // 组合银商检索号+银商流水号作为唯一索引
            String searchNo = fields[5].trim() + fields[6].trim();
            bill.setSearchNo(searchNo);

            bill.setVersionNo(versionNo);

            return bill;

        } catch (Exception e) {
            log.error("解析银商退费账单行失败：{}", line, e);
            return null;
        }
    }

    /**
     * 批量保存银商支付账单
     */
    private boolean saveChinaumsPayBills(List<ChinaumsPayBill> bills, String versionNo) {
        try {
            // 先删除同版本的旧数据
            QueryWrapper<ChinaumsPayBill> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("version_no", versionNo);
            chinaumsPayBillService.remove(deleteWrapper);
            
            // 批量插入新数据
            boolean result = chinaumsPayBillService.saveBatch(bills);
            log.info("批量保存银商支付账单完成，版本：{}，数量：{}", versionNo, bills.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量保存银商支付账单失败", e);
            return false;
        }
    }

    /**
     * 批量保存银商退费账单
     */
    private boolean saveChinaumsRefundBills(List<ChinaumsRefundBill> bills, String versionNo) {
        try {
            // 先删除同版本的旧数据
            QueryWrapper<ChinaumsRefundBill> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("version_no", versionNo);
            chinaumsRefundBillService.remove(deleteWrapper);
            
            // 批量插入新数据
            boolean result = chinaumsRefundBillService.saveBatch(bills);
            log.info("批量保存银商退费账单完成，版本：{}，数量：{}", versionNo, bills.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量保存银商退费账单失败", e);
            return false;
        }
    }
}
