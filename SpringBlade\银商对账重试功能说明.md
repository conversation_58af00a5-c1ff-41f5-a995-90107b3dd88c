# 银商对账重试功能说明

## 功能概述

新增银商对账重试接口，用于重新处理对账失败的记录。该功能可以根据指定的时间段，查找所有对比失败的记录，并根据订单号重新进行对比。

## 主要特性

1. **智能重试**：自动查找指定时间段内的对比失败记录
2. **订单匹配**：根据订单号重新匹配能源平台与银商的账单记录
3. **金额对比**：重新对比金额，更新对比结果
4. **统计更新**：自动更新对账结果统计信息
5. **默认参数**：支持默认时间段（T-1）

## 接口说明

### 1. 重试接口

**接口地址**：`POST /compare-bill/retry-failed`

**请求参数**：
```json
{
    "startDate": "2024-01-01",  // 开始日期，格式：yyyy-MM-dd，可选（默认为昨天）
    "endDate": "2024-01-01",    // 结束日期，格式：yyyy-MM-dd，可选（默认为开始日期）
    "compareType": "5"          // 对比类型，可选（默认为5-能源平台与银商账单比对）
}
```

**响应结果**：
```json
{
    "code": 200,
    "success": true,
    "data": true,               // true表示重试成功，false表示重试失败
    "msg": "操作成功"
}
```

### 2. 对比类型说明

- `1`：银商与移动支付平台账单比对
- `2`：能源平台与星云账单比对
- `3`：能源平台与移动支付平台账单比对
- `4`：能源平台与发行ETC账单比对
- `5`：能源平台与银商账单比对（默认）

## 重试逻辑

### 支付账单重试逻辑（基于明细表内部匹配）

1. 查询指定时间段内对比失败的支付记录（`compareResult = 1`）
2. **逐条处理**失败记录，不再按版本号分组限制
3. **在账单比对明细表内部**根据订单号进行匹配：
   - 如果能源平台订单号为空，根据银商订单号在明细表中查找包含能源平台数据的记录
   - 如果银商订单号为空，根据能源平台订单号在明细表中查找包含银商数据的记录
   - 匹配时排除当前记录，优先选择已平账的记录
4. **数据安全保障**：补充缺失数据时保留原有数据，不会丢失已有信息
5. 金额匹配则标记为平账（`compareResult = 0`），不匹配则保持异常状态（`compareResult = 1`）
6. 更新对账明细记录，保存原始对比结果到 `originalCompareResult` 字段
7. 重新计算并更新所有相关运营商和版本的对账结果统计

### 退款账单重试逻辑（基于明细表内部匹配，处理重复订单号）

1. 查询指定时间段内对比失败的退款记录（`compareResult = 1`）
2. **逐条处理**失败记录，不再按版本号分组限制
3. **在账单比对明细表内部**根据退款订单号进行匹配：
   - 如果能源平台退款数据为空，根据银商退款订单号在明细表中查找包含能源平台数据的记录
   - 如果银商退款数据为空，根据能源平台退款订单号在明细表中查找包含银商数据的记录
   - 匹配时排除当前记录，限制为退款交易类型
4. **处理重复订单号**：
   - 当发现多条相同订单号的退款记录时，首先根据**金额精确匹配**
   - 如果金额匹配失败，使用**退款时间辅助匹配**（同一天视为匹配）
   - 如果仍无法精确匹配，优先返回已平账的记录或最新记录
5. **数据安全保障**：补充缺失数据时保留原有数据，不会丢失已有信息
6. 金额匹配则标记为平账（`compareResult = 0`），不匹配则保持异常状态（`compareResult = 1`）
7. 更新对账明细记录，保存原始对比结果到 `originalCompareResult` 字段
8. 重新计算并更新所有相关运营商和版本的对账结果统计

### 明细表内部匹配的优势

- **数据一致性**：直接在明细表中匹配，确保数据来源一致性
- **避免数据丢失**：补充数据时保留原有信息，不会覆盖已存在的数据
- **处理复杂情况**：能够处理退款订单号重复的特殊情况
- **智能匹配策略**：结合金额和时间进行精确匹配，提高匹配准确性
- **性能优化**：直接在明细表查询，避免跨表关联查询的性能开销

## 使用示例

### 1. 重试昨天的失败记录（使用默认参数）

```bash
curl -X POST http://localhost/compare-bill/retry-failed \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 重试指定日期的失败记录

```bash
curl -X POST http://localhost/compare-bill/retry-failed \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-01",
    "compareType": "5"
  }'
```

### 3. 重试指定时间段的失败记录

```bash
curl -X POST http://localhost/compare-bill/retry-failed \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-07",
    "compareType": "5"
  }'
```

## 注意事项

1. **数据安全**：重试过程会保存原始对比结果到 `originalCompareResult` 字段
2. **性能考虑**：大时间段的重试可能耗时较长，建议分批处理
3. **日志记录**：重试过程会详细记录日志，便于问题排查
4. **事务处理**：每条记录的更新都是独立的，单条失败不影响其他记录
5. **统计更新**：重试完成后会自动重新计算异常记录数并更新统计

## 相关代码文件

- **服务接口**：`ICompareBillResultService.java`
- **服务实现**：`ICompareBillResultServiceImpl.java`
- **控制器**：`CompareBillResultController.java`
- **DTO**：`RetryCompareDTO.java`
- **测试类**：`CompareBillRetryTest.java`

## 测试建议

建议编写单元测试和集成测试来验证重试功能：

1. 测试默认参数的重试
2. 测试指定时间段的重试
3. 测试参数验证
4. 测试异常情况处理
5. 测试统计结果更新

通过测试确保重试功能的稳定性和正确性。
