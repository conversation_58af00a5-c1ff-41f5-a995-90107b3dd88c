package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 银商支付账单实体
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("chinaums_pay_bill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChinaumsPayBill对象", description = "银商支付账单表")
public class ChinaumsPayBill extends BaseEntity {

    /**
     * 充电主订单号
     */
    @ApiModelProperty(value = "充电主订单号")
    private String mainOrderCode;
    /**
     * 支付平台支付订单号
     */
    @ApiModelProperty(value = "支付平台支付订单号")
    private String businessOrderNo;
    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    private String transDate;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private String payAmount;
    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionNo;
    /**
     * 银商检索号+银商流水号（唯一索引）
     */
    @ApiModelProperty(value = "银商检索号+银商流水号（唯一索引）")
    private String searchNo;

}