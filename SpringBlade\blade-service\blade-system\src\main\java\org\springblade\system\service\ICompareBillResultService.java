/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service;

import org.springblade.system.dto.RetryCompareDTO;
import org.springblade.system.entity.CompareBillResult;
import org.springblade.system.vo.CompareBillResultVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface ICompareBillResultService extends BaseService<CompareBillResult> {

    IPage<CompareBillResultVO> selectCompareBillResultPage(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult);

    /**
     * 自定义分页
     *
     * @param page
     * @param compareBillResult
     * @return
     */
    IPage<CompareBillResultVO> selectCompareBillResultPage2(IPage<CompareBillResultVO> page, CompareBillResultVO compareBillResult);

    /**
     * 重试对账
     *
     * @param retryCompareDTO 重试参数：开始日期、结束日期、对账类型
     * @return 是否成功
     */
    boolean retry(RetryCompareDTO retryCompareDTO);

    /**
     * 银商对账重试接口
     * 传入重试时间段（默认T-1），查找该时间内所有对比失败的记录（CompareBillDetail的compareResult为1），
     * 根据订单号重新对比，如果找到相同记录，然后对比金额，将对比结果更新到CompareBillDetail、CompareBillResult
     *
     * @param retryCompareDTO 重试参数：开始日期、结束日期、对账类型
     * @return 是否成功
     */
    boolean retryFailedRecords(RetryCompareDTO retryCompareDTO);

    /**
     * 导出对账数据
     *
     * @param compareBillResult 查询条件
     * @return 文件路径
     */
    byte[] exportData(CompareBillResultVO compareBillResult);

    /**
     * 查询汇总记录
     *
     * @param vo
     * @return
     */
    List<CompareBillResultVO> querySummaryRecords(CompareBillResultVO vo);

    /**
     * 计算汇总数据
     *
     * @param records
     * @return
     */
    Map<String, Object> calculateSummary(List<CompareBillResultVO> records);

    /**
     * 能源平台和银商支付平台支付账单比对
     *
     * @param dateParamc 日期参数
     */
    void nyptAndChinaumsPayBillCompare(int dateParamc);

    /**
     * 能源平台和银商支付平台退款账单比对
     *
     * @param dateParamc 日期参数
     */
    void nyptAndChinaumsRefundBillCompare(int dateParamc);

}
