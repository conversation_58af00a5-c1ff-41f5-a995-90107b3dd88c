package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.core.tool.exception.ServiceException;
import org.springblade.system.entity.OrgInvoiceIssuerMapping;
import org.springblade.system.mapper.OrgInvoiceIssuerMappingMapper;
import org.springblade.system.service.IOrgInvoiceIssuerMappingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class OrgInvoiceIssuerMappingServiceImpl extends ServiceImpl<OrgInvoiceIssuerMappingMapper, OrgInvoiceIssuerMapping> implements IOrgInvoiceIssuerMappingService {

    @Override
    public List<Long> getOrgIdsBySalesId(String salesId) {
        return this.list(Wrappers.<OrgInvoiceIssuerMapping>lambdaQuery()
                .eq(OrgInvoiceIssuerMapping::getSalesId, salesId)
        ).stream().map(m -> {
            try {
                return Long.parseLong(m.getOrgId());
            } catch (NumberFormatException e) {
                return null;
            }
        }).filter(java.util.Objects::nonNull).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMappings(String salesId, List<Long> orgIds) {
        if (salesId == null || salesId.isEmpty()) {
            throw new ServiceException("salesId不能为空");
        }
        // 清除旧关联
        this.remove(Wrappers.<OrgInvoiceIssuerMapping>lambdaQuery()
                .eq(OrgInvoiceIssuerMapping::getSalesId, salesId));
        // 保存新关联
        if (orgIds != null && !orgIds.isEmpty()) {
            List<OrgInvoiceIssuerMapping> list = orgIds.stream().map(id -> {
                OrgInvoiceIssuerMapping m = new OrgInvoiceIssuerMapping();
                m.setSalesId(salesId);
                m.setOrgId(String.valueOf(id));
                return m;
            }).toList();
            return this.saveBatch(list);
        }
        return true;
    }
}

