package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.math.BigDecimal;

@Data
@TableName("org_invoice_issuer")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OrgInvoiceIssuerMapping对象", description = "机构开票方关联表")
public class OrgInvoiceIssuerMapping extends BaseEntity {
private static final long serialVersionUID = 1L;
private String OrgId;
private String salesId;
private String remark;
}
