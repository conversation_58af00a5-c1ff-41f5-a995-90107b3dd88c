#服务器端口
server:
  port: 8200

#数据源配置
#spring:
#  datasource:
#    url: ${blade.datasource.prod.url}
#    username: ${blade.datasource.prod.username}
#    password: ${blade.datasource.prod.password}

spring:
  #排除DruidDataSourceAutoConfigure
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源或者数据源组,默认值即为master
      primary: master
      datasource:
        master:
          url: ${blade.datasource.demo.master.url}
          username: ${blade.datasource.demo.master.username}
          password: ${blade.datasource.demo.master.password}
        slave:
          url: ${blade.datasource.demo.slave.url}
          username: ${blade.datasource.demo.slave.username}
          password: ${blade.datasource.demo.slave.password}
