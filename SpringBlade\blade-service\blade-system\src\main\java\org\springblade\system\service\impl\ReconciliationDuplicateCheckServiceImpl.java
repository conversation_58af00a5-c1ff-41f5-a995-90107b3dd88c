package org.springblade.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.entity.CompareBillResult;
import org.springblade.system.service.ICompareBillDetailService;
import org.springblade.system.service.ICompareBillResultService;
import org.springblade.system.service.IReconciliationDuplicateCheckService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 对账去重检查服务实现
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReconciliationDuplicateCheckServiceImpl implements IReconciliationDuplicateCheckService {

    private final ICompareBillDetailService compareBillDetailService;
    private final ICompareBillResultService compareBillResultService;

    @Override
    public boolean isDuplicateDetail(CompareBillDetail detail) {
        // 检查银商唯一索引
        if (StrUtil.isNotBlank(detail.getChinaumsBusinessOrderNo()) && 
            StrUtil.isNotBlank(detail.getChinaumsSearchNo())) {
            CompareBillDetail existing = findExistingDetailByChinaums(
                detail.getChinaumsBusinessOrderNo(), 
                detail.getChinaumsSearchNo(),
                detail.getCompareType(),
                detail.getTransType()
            );
            if (existing != null) {
                return true;
            }
        }

        // 检查能源平台唯一索引
        if (StrUtil.isNotBlank(detail.getNyptBusinessOrderNo()) && 
            StrUtil.isNotBlank(detail.getNyptRefundId())) {
            CompareBillDetail existing = findExistingDetailByNypt(
                detail.getNyptBusinessOrderNo(),
                detail.getNyptRefundId(),
                detail.getCompareType(),
                detail.getTransType()
            );
            if (existing != null) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean isDuplicateResult(CompareBillResult result) {
        QueryWrapper<CompareBillResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_type", result.getCompareType())
                .eq("trans_type", result.getTransType())
                .eq("operator_id", result.getOperatorId())
                .eq("charge_date", result.getChargeDate());

        return compareBillResultService.count(queryWrapper) > 0;
    }

    @Override
    public CompareBillDetail findExistingDetailByChinaums(String chinaumsBusinessOrderNo, String chinaumsSearchNo,
                                                          String compareType, String transType) {
        if (StrUtil.isBlank(chinaumsBusinessOrderNo) || StrUtil.isBlank(chinaumsSearchNo)) {
            return null;
        }

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("chinaums_business_order_no", chinaumsBusinessOrderNo)
                .eq("chinaums_search_no", chinaumsSearchNo)
                .eq("compare_type", compareType)
                .eq("trans_type", transType);

        return compareBillDetailService.getOne(queryWrapper);
    }

    @Override
    public CompareBillDetail findExistingDetailByNypt(String nyptBusinessOrderNo, String nyptRefundId,
                                                      String compareType, String transType) {
        if (StrUtil.isBlank(nyptBusinessOrderNo) || StrUtil.isBlank(nyptRefundId)) {
            return null;
        }

        QueryWrapper<CompareBillDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nypt_business_order_no", nyptBusinessOrderNo)
                .eq("nypt_refund_id", nyptRefundId)
                .eq("compare_type", compareType)
                .eq("trans_type", transType);

        return compareBillDetailService.getOne(queryWrapper);
    }

    @Override
    public CompareBillDetail updateExistingDetail(CompareBillDetail existingDetail, CompareBillDetail newDetail) {
        log.info("更新已存在的对账明细记录，ID：{}", existingDetail.getId());

        // 保存原始值（如果还没有保存过）
        if (StrUtil.isBlank(existingDetail.getOriginalCompareResult())) {
            existingDetail.setOriginalCompareResult(existingDetail.getCompareResult());
        }
        if (StrUtil.isBlank(existingDetail.getOriginalNyptPayAmount())) {
            existingDetail.setOriginalNyptPayAmount(existingDetail.getNyptPayAmount());
        }
        if (StrUtil.isBlank(existingDetail.getOriginalChinaumsPayAmount())) {
            existingDetail.setOriginalChinaumsPayAmount(existingDetail.getChinaumsPayAmount());
        }

        // 更新银商数据（如果新记录有银商数据）
        if (StrUtil.isNotBlank(newDetail.getChinaumsBusinessOrderNo())) {
            existingDetail.setChinaumsBusinessOrderNo(newDetail.getChinaumsBusinessOrderNo());
            existingDetail.setChinaumsTransDate(newDetail.getChinaumsTransDate());
            existingDetail.setChinaumsPayAmount(newDetail.getChinaumsPayAmount());
            existingDetail.setChinaumsSearchNo(newDetail.getChinaumsSearchNo());
        }

        // 更新能源平台数据（如果新记录有能源平台数据）
        if (StrUtil.isNotBlank(newDetail.getNyptBusinessOrderNo())) {
            existingDetail.setNyptBusinessOrderNo(newDetail.getNyptBusinessOrderNo());
            existingDetail.setNyptTransDate(newDetail.getNyptTransDate());
            existingDetail.setNyptPayAmount(newDetail.getNyptPayAmount());
            existingDetail.setNyptRefundId(newDetail.getNyptRefundId());
        }

        // 更新其他字段
        if (StrUtil.isNotBlank(newDetail.getMainOrderCode())) {
            existingDetail.setMainOrderCode(newDetail.getMainOrderCode());
        }
        if (StrUtil.isNotBlank(newDetail.getOperatorId())) {
            existingDetail.setOperatorId(newDetail.getOperatorId());
        }
        if (StrUtil.isNotBlank(newDetail.getFundsFrom())) {
            existingDetail.setFundsFrom(newDetail.getFundsFrom());
        }

        // 更新对比结果和平账状态
        existingDetail.setCompareResult(newDetail.getCompareResult());
        existingDetail.setBalanceStatus(newDetail.getBalanceStatus());
        existingDetail.setVersionNo(newDetail.getVersionNo());
        existingDetail.setUpdateTime(new Date());

        // 更新备注信息
        String existingRemark = StrUtil.isBlank(existingDetail.getRemark()) ? "" : existingDetail.getRemark();
        String newRemark = StrUtil.isBlank(newDetail.getRemark()) ? "" : newDetail.getRemark();
        if (StrUtil.isNotBlank(newRemark) && !existingRemark.contains(newRemark)) {
            existingDetail.setRemark(existingRemark + (StrUtil.isNotBlank(existingRemark) ? "；" : "") + newRemark);
        }

        return existingDetail;
    }
}
