package org.springblade.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.entity.CompareBillResult;
import org.springblade.system.enums.ReconciliationCompareEnum;
import org.springblade.system.service.ICompareBillDetailService;
import org.springblade.system.service.ICompareBillResultService;
import org.springblade.system.service.IDailyReconciliationService;
import org.springblade.system.vo.DailyReconciliationVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 日对账结果统计服务实现
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class DailyReconciliationServiceImpl implements IDailyReconciliationService {

    private final ICompareBillResultService compareBillResultService;
    private final ICompareBillDetailService compareBillDetailService;

    @Override
    public boolean generateDailyResult(Date date) {
        log.info("开始生成日对账结果统计，日期：{}", DateUtil.formatDate(date));
        
        try {
            String dateStr = DateUtil.format(date, "yyyy-MM-dd");
            String versionNo = DateUtil.format(date, "yyyyMMdd");
            
            // 查询该日期的对账结果
            QueryWrapper<CompareBillResult> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("charge_date", dateStr)
                    .eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            
            List<CompareBillResult> results = compareBillResultService.list(queryWrapper);
            
            if (results.isEmpty()) {
                log.warn("未找到对账结果数据，日期：{}", dateStr);
                return false;
            }
            
            // 按运营商分组统计
            Map<String, List<CompareBillResult>> operatorGroups = results.stream()
                    .collect(Collectors.groupingBy(CompareBillResult::getOperatorId));
            
            for (Map.Entry<String, List<CompareBillResult>> entry : operatorGroups.entrySet()) {
                String operatorId = entry.getKey();
                List<CompareBillResult> operatorResults = entry.getValue();
                
                // 生成该运营商的日统计
                generateOperatorDailyResult(operatorId, dateStr, versionNo, operatorResults);
            }
            
            log.info("日对账结果统计生成完成，日期：{}", dateStr);
            return true;
            
        } catch (Exception e) {
            log.error("生成日对账结果统计失败", e);
            return false;
        }
    }

    @Override
    public List<DailyReconciliationVO> queryDailyResults(String startDate, String endDate, String operatorId) {
        log.info("查询日对账结果，开始日期：{}，结束日期：{}，运营商：{}", startDate, endDate, operatorId);
        
        try {
            QueryWrapper<CompareBillResult> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
            
            if (StrUtil.isNotBlank(startDate)) {
                queryWrapper.ge("charge_date", startDate);
            }
            if (StrUtil.isNotBlank(endDate)) {
                queryWrapper.le("charge_date", endDate);
            }
            if (StrUtil.isNotBlank(operatorId)) {
                queryWrapper.eq("operator_id", operatorId);
            }
            
            queryWrapper.orderByDesc("charge_date");
            
            List<CompareBillResult> results = compareBillResultService.list(queryWrapper);
            
            // 转换为VO并按日期和运营商分组
            Map<String, Map<String, List<CompareBillResult>>> dateOperatorGroups = results.stream()
                    .collect(Collectors.groupingBy(CompareBillResult::getChargeDate,
                            Collectors.groupingBy(CompareBillResult::getOperatorId)));
            
            List<DailyReconciliationVO> voList = new ArrayList<>();
            
            for (Map.Entry<String, Map<String, List<CompareBillResult>>> dateEntry : dateOperatorGroups.entrySet()) {
                String date = dateEntry.getKey();
                Map<String, List<CompareBillResult>> operatorGroups = dateEntry.getValue();
                
                for (Map.Entry<String, List<CompareBillResult>> operatorEntry : operatorGroups.entrySet()) {
                    String operator = operatorEntry.getKey();
                    List<CompareBillResult> operatorResults = operatorEntry.getValue();
                    
                    DailyReconciliationVO vo = buildDailyReconciliationVO(date, operator, operatorResults);
                    voList.add(vo);
                }
            }
            
            // 按日期倒序排列
            voList.sort((a, b) -> b.getReconciliationDate().compareTo(a.getReconciliationDate()));
            
            return voList;
            
        } catch (Exception e) {
            log.error("查询日对账结果失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean needReReconciliation(Date date) {
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");
        
        // 检查是否存在对账明细记录
        QueryWrapper<CompareBillDetail> detailWrapper = new QueryWrapper<>();
        detailWrapper.eq("version_no", DateUtil.format(date, "yyyyMMdd"))
                .eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
        
        long detailCount = compareBillDetailService.count(detailWrapper);
        
        if (detailCount == 0) {
            log.info("日期 {} 没有对账明细记录，需要重新对账", dateStr);
            return true;
        }
        
        // 检查是否存在对账结果记录
        QueryWrapper<CompareBillResult> resultWrapper = new QueryWrapper<>();
        resultWrapper.eq("charge_date", dateStr)
                .eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
        
        long resultCount = compareBillResultService.count(resultWrapper);
        
        if (resultCount == 0) {
            log.info("日期 {} 没有对账结果记录，需要重新对账", dateStr);
            return true;
        }
        
        return false;
    }

    @Override
    public String getReconciliationStatus(Date date, String operatorId) {
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");
        
        QueryWrapper<CompareBillResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("charge_date", dateStr)
                .eq("operator_id", operatorId)
                .eq("compare_type", ReconciliationCompareEnum.COMPARE_TYPE.NYPT_CHINAUMS.getCode());
        
        List<CompareBillResult> results = compareBillResultService.list(queryWrapper);
        
        if (results.isEmpty()) {
            return "0"; // 未执行
        }
        
        // 检查是否有异常记录
        boolean hasException = results.stream().anyMatch(result -> result.getAbnormalCount() > 0);
        
        return hasException ? "3" : "2"; // 3-执行失败（有异常）；2-执行成功
    }

    @Override
    public boolean updateReconciliationStatus(Date date, String operatorId, String status) {
        // 这里可以扩展为更新专门的状态表
        // 目前通过对账结果表的异常记录数来判断状态
        log.info("更新对账执行状态，日期：{}，运营商：{}，状态：{}", DateUtil.formatDate(date), operatorId, status);
        return true;
    }

    /**
     * 生成运营商日统计
     */
    private void generateOperatorDailyResult(String operatorId, String dateStr, String versionNo, 
                                           List<CompareBillResult> results) {
        log.info("生成运营商日统计，运营商：{}，日期：{}", operatorId, dateStr);
        
        // 这里可以扩展为保存到专门的日统计表
        // 目前通过CompareBillResult表来提供统计数据
    }

    /**
     * 构建日对账结果VO
     */
    private DailyReconciliationVO buildDailyReconciliationVO(String date, String operatorId, 
                                                           List<CompareBillResult> results) {
        DailyReconciliationVO vo = new DailyReconciliationVO();
        vo.setReconciliationDate(date);
        vo.setOperatorId(operatorId);
        
        // 分别统计支付和退费数据
        CompareBillResult payResult = results.stream()
                .filter(r -> ReconciliationCompareEnum.TRANS_TYPE.PAY.getCode().equals(r.getTransType()))
                .findFirst().orElse(null);
        
        CompareBillResult refundResult = results.stream()
                .filter(r -> ReconciliationCompareEnum.TRANS_TYPE.REFUND.getCode().equals(r.getTransType()))
                .findFirst().orElse(null);
        
        // 支付数据统计
        if (payResult != null) {
            vo.setPayRecordCount(Integer.parseInt(payResult.getNyptTotal()));
            vo.setPayAmount(new BigDecimal(payResult.getNyptAmount()));
            vo.setPayExceptionCount(payResult.getAbnormalCount());
        } else {
            vo.setPayRecordCount(0);
            vo.setPayAmount(BigDecimal.ZERO);
            vo.setPayExceptionCount(0);
        }
        
        // 退费数据统计
        if (refundResult != null) {
            vo.setRefundRecordCount(Integer.parseInt(refundResult.getNyptTotal()));
            vo.setRefundAmount(new BigDecimal(refundResult.getNyptAmount()));
            vo.setRefundExceptionCount(refundResult.getAbnormalCount());
        } else {
            vo.setRefundRecordCount(0);
            vo.setRefundAmount(BigDecimal.ZERO);
            vo.setRefundExceptionCount(0);
        }
        
        // 总计数据
        vo.setTotalRecordCount(vo.getPayRecordCount() + vo.getRefundRecordCount());
        vo.setTotalAmount(vo.getPayAmount().add(vo.getRefundAmount()));
        vo.setTotalExceptionCount(vo.getPayExceptionCount() + vo.getRefundExceptionCount());
        
        // 计算异常率
        if (vo.getTotalRecordCount() > 0) {
            BigDecimal rate = new BigDecimal(vo.getTotalExceptionCount())
                    .divide(new BigDecimal(vo.getTotalRecordCount()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            vo.setExceptionRate(rate.setScale(2, RoundingMode.HALF_UP) + "%");
        } else {
            vo.setExceptionRate("0.00%");
        }
        
        // 设置状态
        vo.setStatus(vo.getTotalExceptionCount() > 0 ? "3" : "2");
        
        return vo;
    }
}
