package org.springblade.system.service;

import org.springblade.system.entity.CompareBillDetail;
import org.springblade.system.entity.CompareBillResult;

/**
 * 对账去重检查服务接口
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IReconciliationDuplicateCheckService {

    /**
     * 检查CompareBillDetail是否存在重复记录
     * @param detail 对账明细
     * @return 是否存在重复
     */
    boolean isDuplicateDetail(CompareBillDetail detail);

    /**
     * 检查CompareBillResult是否存在重复记录
     * @param result 对账结果
     * @return 是否存在重复
     */
    boolean isDuplicateResult(CompareBillResult result);

    /**
     * 根据银商唯一索引查找已存在的对账明细
     * @param chinaumsBusinessOrderNo 银商订单号
     * @param chinaumsSearchNo 银商检索号+流水号
     * @param compareType 比对类型
     * @param transType 交易类型
     * @return 已存在的对账明细，如果不存在返回null
     */
    CompareBillDetail findExistingDetailByChinaums(String chinaumsBusinessOrderNo, String chinaumsSearchNo, 
                                                   String compareType, String transType);

    /**
     * 根据能源平台唯一索引查找已存在的对账明细
     * @param nyptBusinessOrderNo 能源平台订单号
     * @param nyptRefundId 能源平台退费ID
     * @param compareType 比对类型
     * @param transType 交易类型
     * @return 已存在的对账明细，如果不存在返回null
     */
    CompareBillDetail findExistingDetailByNypt(String nyptBusinessOrderNo, String nyptRefundId, 
                                               String compareType, String transType);

    /**
     * 更新已存在的对账明细记录
     * @param existingDetail 已存在的记录
     * @param newDetail 新的对账明细数据
     * @return 更新后的记录
     */
    CompareBillDetail updateExistingDetail(CompareBillDetail existingDetail, CompareBillDetail newDetail);
}
