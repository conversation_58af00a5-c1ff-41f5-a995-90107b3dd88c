# 银商对账重试逻辑示例说明

## 场景示例

### 场景1：支付账单缺失能源平台数据

**原始状态**：
```
明细记录A：
- ID: 1001
- 银商订单号: CMS20240101001
- 银商金额: 100.00
- 能源平台订单号: null (缺失)
- 能源平台金额: null (缺失)
- 对比结果: 1 (异常)

明细记录B：
- ID: 1002  
- 银商订单号: null
- 银商金额: null
- 能源平台订单号: CMS20240101001
- 能源平台金额: 100.00
- 对比结果: 1 (异常)
```

**重试过程**：
1. 处理记录A，发现能源平台数据缺失
2. 根据银商订单号"CMS20240101001"在明细表中查找
3. 找到记录B，其中包含相同订单号的能源平台数据
4. 将记录B的能源平台数据补充到记录A中
5. 重新对比金额：100.00 = 100.00，匹配成功

**重试结果**：
```
明细记录A（更新后）：
- ID: 1001
- 银商订单号: CMS20240101001
- 银商金额: 100.00
- 能源平台订单号: CMS20240101001 (已补充)
- 能源平台金额: 100.00 (已补充)
- 对比结果: 0 (正常)
- 原始对比结果: 1 (保存原始状态)
```

### 场景2：退款订单号重复处理

**原始状态**：
```
明细记录A：
- ID: 2001
- 银商退款订单号: REF20240101001
- 银商退款金额: 50.00
- 银商退款时间: 2024-01-01 10:00:00
- 能源平台退款订单号: null (缺失)
- 对比结果: 1 (异常)

明细记录B：
- ID: 2002
- 银商退款订单号: null
- 能源平台退款订单号: REF20240101001
- 能源平台退款金额: 30.00
- 能源平台退款时间: 2024-01-01 09:30:00
- 对比结果: 1 (异常)

明细记录C：
- ID: 2003
- 银商退款订单号: null
- 能源平台退款订单号: REF20240101001
- 能源平台退款金额: 50.00
- 能源平台退款时间: 2024-01-01 10:15:00
- 对比结果: 1 (异常)
```

**重试过程**：
1. 处理记录A，发现能源平台退款数据缺失
2. 根据银商退款订单号"REF20240101001"在明细表中查找
3. 发现多条记录（B和C）包含相同的能源平台退款订单号
4. **金额匹配**：记录B金额30.00 ≠ 50.00，记录C金额50.00 = 50.00 ✓
5. 选择记录C进行数据补充
6. 重新对比金额：50.00 = 50.00，匹配成功

**重试结果**：
```
明细记录A（更新后）：
- ID: 2001
- 银商退款订单号: REF20240101001
- 银商退款金额: 50.00
- 银商退款时间: 2024-01-01 10:00:00
- 能源平台退款订单号: REF20240101001 (已补充)
- 能源平台退款金额: 50.00 (已补充)
- 能源平台退款时间: 2024-01-01 10:15:00 (已补充)
- 对比结果: 0 (正常)
- 原始对比结果: 1 (保存原始状态)
```

### 场景3：时间辅助匹配

**当金额匹配失败时**：
```
明细记录A：
- 银商退款订单号: REF20240101002
- 银商退款金额: 80.00
- 银商退款时间: 2024-01-01 14:00:00

候选记录B：
- 能源平台退款订单号: REF20240101002
- 能源平台退款金额: 75.00 (金额不匹配)
- 能源平台退款时间: 2024-01-01 14:30:00

候选记录C：
- 能源平台退款订单号: REF20240101002
- 能源平台退款金额: 85.00 (金额不匹配)
- 能源平台退款时间: 2024-01-02 09:00:00
```

**匹配逻辑**：
1. 金额匹配失败（80.00 ≠ 75.00 且 80.00 ≠ 85.00）
2. 启用时间辅助匹配
3. 记录B：2024-01-01 = 2024-01-01 ✓（同一天）
4. 记录C：2024-01-01 ≠ 2024-01-02 ✗（不同天）
5. 选择记录B进行数据补充

## 数据安全保障

### 保护原有数据
```java
// 只在字段为空时才补充，不覆盖已有数据
if (StringUtils.isBlank(detail.getNyptBusinessOrderNo()) && StringUtils.isNotBlank(matchedDetail.getNyptBusinessOrderNo())) {
    detail.setNyptBusinessOrderNo(matchedDetail.getNyptBusinessOrderNo());
}

// 保留或补充其他字段
if (StringUtils.isBlank(detail.getMainOrderCode()) && StringUtils.isNotBlank(matchedDetail.getMainOrderCode())) {
    detail.setMainOrderCode(matchedDetail.getMainOrderCode());
}
```

### 原始状态保存
```java
// 保存原始对比结果，便于追踪
if (detail.getOriginalCompareResult() == null) {
    detail.setOriginalCompareResult(originalCompareResult);
}
```

## 匹配优先级

### 支付账单匹配优先级
1. 已平账记录（compareResult = 0）
2. 创建时间最新的记录

### 退款账单匹配优先级
1. 金额精确匹配
2. 时间辅助匹配（同一天）
3. 已平账记录
4. 创建时间最新的记录

## 日志记录

系统会详细记录重试过程：
- 匹配成功：记录订单号、匹配方式、金额信息
- 多条记录：记录发现的记录数量和匹配策略
- 匹配失败：记录无法匹配的原因和最终选择

这些日志有助于问题排查和重试效果分析。
