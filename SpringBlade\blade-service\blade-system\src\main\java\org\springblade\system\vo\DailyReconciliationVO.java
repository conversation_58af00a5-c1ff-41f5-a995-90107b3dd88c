package org.springblade.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 日对账结果VO
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@ApiModel(value = "DailyReconciliationVO", description = "日对账结果")
public class DailyReconciliationVO {

    @ApiModelProperty(value = "对账日期")
    private String reconciliationDate;

    @ApiModelProperty(value = "运营商ID")
    private String operatorId;

    @ApiModelProperty(value = "运营商名称")
    private String operatorName;

    @ApiModelProperty(value = "支付交易记录数")
    private Integer payRecordCount;

    @ApiModelProperty(value = "支付交易金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付异常记录数")
    private Integer payExceptionCount;

    @ApiModelProperty(value = "退费交易记录数")
    private Integer refundRecordCount;

    @ApiModelProperty(value = "退费交易金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退费异常记录数")
    private Integer refundExceptionCount;

    @ApiModelProperty(value = "总交易记录数")
    private Integer totalRecordCount;

    @ApiModelProperty(value = "总交易金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "总异常记录数")
    private Integer totalExceptionCount;

    @ApiModelProperty(value = "对账执行状态（0-未执行；1-执行中；2-执行成功；3-执行失败）")
    private String status;

    @ApiModelProperty(value = "对账执行时间")
    private String executeTime;

    @ApiModelProperty(value = "异常率")
    private String exceptionRate;
}
