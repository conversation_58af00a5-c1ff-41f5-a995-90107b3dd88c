package org.springblade.system.service;

import org.springblade.system.dto.ManualMatchDTO;
import org.springblade.system.entity.CompareBillDetail;

import java.util.List;

/**
 * 对账异常处理服务接口
 * 
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IReconciliationExceptionService {

    /**
     * 查询异常对账记录
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param operatorId 运营商ID
     * @param transType 交易类型（1-支付；2-退款）
     * @return 异常记录列表
     */
    List<CompareBillDetail> queryExceptionRecords(String startDate, String endDate, 
                                                  String operatorId, String transType);

    /**
     * 手动匹配异常记录
     * @param manualMatchDTO 手动匹配参数
     * @return 匹配结果
     */
    boolean manualMatch(ManualMatchDTO manualMatchDTO);

    /**
     * 强制标记为正常
     * @param detailId 对账明细ID
     * @param operatorId 操作员ID
     * @param remark 备注
     * @return 操作结果
     */
    boolean forceMarkAsNormal(Long detailId, String operatorId, String remark);

    /**
     * 查找可匹配的记录
     * @param orderNo 订单号
     * @param amount 金额
     * @param platform 平台（nypt-能源平台；chinaums-银商）
     * @param transType 交易类型
     * @return 可匹配的记录列表
     */
    List<CompareBillDetail> findMatchableRecords(String orderNo, String amount, 
                                                 String platform, String transType);

    /**
     * 交换匹配记录的数据
     * @param sourceDetailId 源记录ID
     * @param targetDetailId 目标记录ID
     * @return 交换结果
     */
    boolean swapRecordData(Long sourceDetailId, Long targetDetailId);

    /**
     * 批量处理异常记录
     * @param detailIds 记录ID列表
     * @param action 处理动作（match-匹配；ignore-忽略；retry-重试）
     * @param operatorId 操作员ID
     * @return 处理结果
     */
    boolean batchProcessExceptions(List<Long> detailIds, String action, String operatorId);
}
