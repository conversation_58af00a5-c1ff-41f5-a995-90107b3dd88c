package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 能源平台支付账单实体
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("nypt_pay_bill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NyptPayBill对象", description = "能源平台支付账单表")
public class NyptPayBill extends BaseEntity {

    /**
     * 充电订单号
     */
    @ApiModelProperty(value = "充电订单号")
    private String mainOrderCode;
    /**
     * 三方支付订单号
     */
    @ApiModelProperty(value = "三方支付订单号")
    private String businessOrderNo;
    /**
     * 交易时间
     */
    @ApiModelProperty(value = "支付完成时间（yyyy-MM-dd HH:mm:ss）")
    private String transDate;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private String payAmount;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号为对账日期N-1日（yyyyMMdd）")
    private String versionNo;
    /**
     * 运营商ID
     */
    @ApiModelProperty(value = "运营商ID")
    private String operatorId;
    /**
     * 运营商编码
     */
    @ApiModelProperty(value = "运营商编码")
    private String operatorCode;
    /**
     * 发票主体ID
     */
    @ApiModelProperty(value = "发票主体ID")
    private String invoiceSubjectId;
    /**
     * 发票主体编码
     */
    @ApiModelProperty(value = "发票主体编码")
    private String invoiceSubjectCode;
    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String fundsFrom;
    /**
     * 支付来源 1-微信小程序
     */
    @ApiModelProperty(value = "支付来源")
    private String source;
}