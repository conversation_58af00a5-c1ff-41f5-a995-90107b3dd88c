package org.springblade.refund.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.refund.entity.CompareBillDetail;
import org.springblade.refund.vo.CompareBillDetailVO;

/**
 * 对账明细表 服务类
 *
 * <AUTHOR>
 */
public interface ICompareBillDetailService extends IService<CompareBillDetail> {

    /**
     * 自定义分页
     *
     * @param page
     * @param compareBillDetail
     * @return
     */
    IPage<CompareBillDetailVO> selectCompareBillDetailPage(IPage<CompareBillDetailVO> page, CompareBillDetailVO compareBillDetail);

}