<template>
  <basic-container>
    <!-- 汇总信息 -->
    <el-card class="summary-card" v-if="summaryData.hasException">
      <div slot="header" class="clearfix">
        <span>对账汇总信息 {{ showchargeDate }}</span>
      </div>
      <div class="summary-content">
        <div class="summary-item">
          <span class="label">能源平台：</span>
            <span>
              支付：<span :class="{ 'highlight': summaryData.payMismatch }">{{ summaryData.nyptPayTotal }}</span>，
              退款：<span :class="{ 'highlight': summaryData.refundMismatch }">{{ summaryData.nyptRefundTotal }}</span>，
              利润：<span :class="{ 'highlight': summaryData.profitMismatch }">{{ summaryData.nyptProfit }}</span>
            </span>
        </div>
        <div class="summary-item">
          <span class="label">银商：</span>
            <span>
              支付：<span :class="{ 'highlight': summaryData.payMismatch }">{{ summaryData.chinaumsPayTotal }}</span>，
              退款：<span :class="{ 'highlight': summaryData.refundMismatch }">{{ summaryData.chinaumsRefundTotal }}</span>，
              利润：<span :class="{ 'highlight': summaryData.profitMismatch }">{{ summaryData.chinaumsProfit }}</span>
            </span>
        </div>
      </div>
    </el-card>
    <el-card class="summary-card" v-else>
      <div slot="header" class="clearfix">
        <span>对账汇总信息 {{ showchargeDate }}</span>
      </div>
      <div class="summary-content">
        <div class="summary-item">
          <span class="label">支付：</span>
          <span>{{ summaryData.payTotal }}</span>
        </div>
        <div class="summary-item">
          <span class="label">退款：</span>
          <span>{{ summaryData.refundTotal }}</span>
        </div>
        <div class="summary-item">
          <span class="label">利润：</span>
          <span>{{ summaryData.profit }}</span>
        </div>
      </div>
    </el-card>
      <!-- :before-open="beforeOpen" -->
      <!-- @row-save="rowSave" -->
      <!-- @row-update="rowUpdate" -->
      <!-- @row-del="rowDel" -->
      <!-- @row-click="rowClick" -->
    <!-- <avue-crud
      :option="option"
      :data="data"
      :page.sync="page"
      :table-loading="loading"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-refresh"
          @click="handleRetry"
        >重试对账</el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-download"
          @click="handleExport"
        >导出</el-button>
      </template>
      <template slot-scope="{row}" slot="compareStatus">
        <el-tag
          :type="row.compareStatus === '0' ? 'success' : 'danger'"
        >{{ row.compareStatus === '0' ? '正常' : '异常' }}</el-tag>
      </template>
      <template slot-scope="{row}" slot="transType">
        <el-tag
          :type="row.transType === '1' ? 'primary' : 'warning'"
        >{{ row.transType === '1' ? '支付' : '退款' }}</el-tag>
      </template>
    </avue-crud> -->
    


    <!-- <avue-crud
      :option="detailOption"
      :data="detailData"
      :page.sync="detailPage"
      :table-loading="detailLoading"
      @current-change="detailCurrentChange"
      @size-change="detailSizeChange"
      @refresh-change="detailRefreshChange"
      @on-load="onDetailLoad"
    >
      <template slot-scope="{row}" slot="compareResult">
        <el-tag
          :type="row.compareResult === '0' ? 'success' : 'danger'"
        >{{ row.compareResult === '0' ? '正常' : '异常' }}</el-tag>
      </template>
      <template slot-scope="{row}" slot="transType">
        <el-tag
          :type="row.transType === '1' ? 'primary' : 'warning'"
        >{{ row.transType === '1' ? '支付' : '退款' }}</el-tag>
      </template>
    </avue-crud> -->

    <!-- 明细表格 -->
    <!-- <el-dialog
      title="对账明细"
      :visible.sync="detailVisible"
      width="90%"
      :before-close="handleDetailClose"
    >
      <avue-crud
        :option="detailOption"
        :data="detailData"
        :page.sync="detailPage"
        :table-loading="detailLoading"
        @current-change="detailCurrentChange"
        @size-change="detailSizeChange"
        @refresh-change="detailRefreshChange"
        @on-load="onDetailLoad"
      >
        <template slot-scope="{row}" slot="compareResult">
          <el-tag
            :type="row.compareResult === '0' ? 'success' : 'danger'"
          >{{ row.compareResult === '0' ? '正常' : '异常' }}</el-tag>
        </template>
        <template slot-scope="{row}" slot="transType">
          <el-tag
            :type="row.transType === '1' ? 'primary' : 'warning'"
          >{{ row.transType === '1' ? '支付' : '退款' }}</el-tag>
        </template>
      </avue-crud>
    </el-dialog> -->

    <!-- 明细表格 -->
    <el-card class="detail-card">
      <div slot="header" class="clearfix">
        <span>对账明细</span>
        <div class="header-buttons">
          <el-button
            v-show="permissionList.retryBtn"
            style="margin-right: 5px;"
            type="primary"
            size="small"
            icon="el-icon-refresh"
            @click="handleRetry"
          >重试对账</el-button>
          <el-button
            v-show="permissionList.exportBtn"
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="handleExport"
          >导出</el-button>
        </div>
      </div>
      <avue-crud
        ref="detailCrud"
        :option="detailOption"
        :data="detailData"
        :page.sync="detailPage"
        :table-loading="detailLoading"
        @current-change="detailCurrentChange"
        @size-change="detailSizeChange"
        @refresh-change="detailRefreshChange"
        @search-change="detailSearchChange"
        @search-reset="detailSearchReset"
        @on-load="onLoad"
      >
        <template slot-scope="{row}" slot="compareResult">
          <el-tag
            :type="row.compareResult === '0' ? 'success' : 'danger'"
          >{{ row.compareResult === '0' ? '正常' : '异常' }}</el-tag>
        </template>
        <template slot-scope="{row}" slot="transType">
          <el-tag
            :type="row.transType === '1' ? 'primary' : 'warning'"
          >{{ row.transType === '1' ? '支付' : '退款' }}</el-tag>
        </template>
      </avue-crud>
    </el-card>

    <!-- 重试对账弹窗 -->
    <el-dialog
      title="重试对账"
      :visible.sync="retryVisible"
      append-to-body
      width="70%"
      :before-close="handleRetryClose"
    >
      <el-form :model="retryForm" label-width="100px" :inline="true">
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="retryForm.startDate"
            type="date"
            placeholder="选择开始日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="retryForm.endDate"
            type="date"
            placeholder="选择结束日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="retryLoading" :disabled="retryLoading" @click="confirmRetry">执行重试</el-button>
          <el-button @click="retryVisible = false" :disabled="retryLoading">取 消</el-button>
        </el-form-item>
      </el-form>

      <!-- 每日对账信息列表（支持筛选） -->
      <avue-crud
        ref="dailyCrud"
        :option="dailyOption"
        :data="dailyData"
        :page.sync="dailyPage"
        :table-loading="dailyLoading"
        @current-change="dailyCurrentChange"
        @size-change="dailySizeChange"
        @refresh-change="dailyRefreshChange"
        @search-change="dailySearchChange"
        @search-reset="dailySearchReset"
        @selection-change="dailySelectionChange"
        @on-load="onDailyLoad"
      >
        <template slot-scope="{row}" slot="status">
          <el-tag :type="row.status === '2' ? 'success' : (row.status === '3' ? 'danger' : 'info')">
            {{ row.status === '2' ? '成功' : (row.status === '3' ? '失败' : '待处理') }}
          </el-tag>
        </template>
        <template slot-scope="{row}" slot="compareStatus">
          <el-tag :type="row.compareStatus === '0' ? 'success' : 'danger'">{{ row.compareStatus === '0' ? '正常' : '异常' }}</el-tag>
        </template>
        <template slot-scope="{row}" slot="transType">
          <el-tag :type="row.transType === '1' ? 'primary' : 'warning'">{{ row.transType === '1' ? '支付' : '退款' }}</el-tag>
        </template>
      </avue-crud>

      <!-- 进度指示器 -->
      <div v-if="retryLoading" style="margin-top: 10px;">
        <el-progress :text-inside="true" :stroke-width="18" :percentage="retryProgress"></el-progress>
        <div style="margin-top: 6px; color: #909399;">正在处理第 {{ retryCurrentStep }} 天，共 {{ retryTotalStep }} 天</div>
        <div style="margin-top: 6px;">
          <el-button size="mini" @click="retryCancelFlag = true">取消执行</el-button>
        </div>
      </div>

      <!-- 执行结果摘要 -->
      <div v-if="retryResultSummary" style="margin-top: 10px;">
        <el-alert
          title="执行结果摘要"
          type="success"
          :closable="false"
          show-icon>
          <template #default>
            <div>总天数：{{ retryResultSummary.totalDays }} 天</div>
            <div>成功天数：{{ retryResultSummary.successDays }} 天</div>
            <div>失败天数：{{ retryResultSummary.failDays }} 天</div>
            <div>仍有异常记录：{{ retryResultSummary.stillAbnormal }} 条</div>
          </template>
        </el-alert>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="retryVisible = false" :disabled="retryLoading">关 闭</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getListWithDetails, retry, exportData, getDailyComparePage } from "@/api/cdpt/compare.js";
import { getList as getPaymentReceiverList } from "@/api/chargeStationLink/paymentReceiver";
import { getList as getOperatorList } from "@/api/chargeStationLink/chargeOperatorInfo";
import { mapGetters } from "vuex";
import { COMPARE_TYPE } from '@/enums/reconciliationCompareEnum'

// 获取T-1的日期
const currentDate = new Date();
let tMinusOne = new Date(currentDate);  // 创建 currentDate 的副本
tMinusOne.setDate(currentDate.getDate() - 1);  // 将日期调整为前一天

export default {
  // name: 'CompareBill',
  data() {
    return {
      // 表格数据
      // data: [],
      // 表格选中数据
      // selectionList: [],
      // 表格加载状态
      // loading: true,
      // 汇总数据
      summaryData: {
        payTotal: '0.00',
        refundTotal: '0.00',
        profit: '0.00',
        nyptPayTotal: '0.00',
        nyptRefundTotal: '0.00',
        nyptProfit: '0.00',
        chinaumsPayTotal: '0.00',
        chinaumsRefundTotal: '0.00',
        chinaumsProfit: '0.00',
        payMismatch: false,
        refundMismatch: false,
        profitMismatch: false,
        hasException: false
      },
      // 表格配置
      // option: {
      //   height: 'auto',
      //   calcHeight: 80,
      //   searchShow: true,
      //   searchMenuSpan: 6,
      //   tip: false,
      //   border: true,
      //   index: true,
      //   viewBtn: false,
      //   addBtn: false,
      //   editBtn: false,
      //   delBtn: false,
      //   selection: false,
      //   dialogClickModal: false,
      //   column: [
      //     {
      //       label: '运营商ID',
      //       prop: 'operatorId',
      //       search: true,
      //       rules: [{
      //         required: true,
      //         message: '请输入运营商ID',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '支付渠道',
      //       prop: 'fundsFrom',
      //       type: 'select',
      //       search: true,
      //       dicData: [
      //         {
      //           label: '银商(微信)',
      //           value: 1
      //         },
      //         {
      //           label: 'ETC',
      //           value: 2
      //         }
      //       ],
      //       rules: [{
      //         required: true,
      //         message: '请选择支付渠道',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '对账日期',
      //       prop: 'chargeDate',
      //       search: true,
      //       searchRange: true,
      //       searchSpan: 12,
      //       type: 'date',
      //       valueFormat: 'yyyy-MM-dd',
      //       value: (() => {
      //         const yesterday = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
      //         const yesterdayStr = this.formatDate(yesterday, 'yyyy-MM-dd');
      //         return [yesterdayStr, yesterdayStr];
      //       })(),
      //       rules: [{
      //         required: true,
      //         message: '请选择对账日期',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '对比状态',
      //       prop: 'compareStatus',
      //       type: 'select',
      //       search: true,
      //       slot: true,
      //       dicData: [
      //         {
      //           label: '正常',
      //           value: '0'
      //         },
      //         {
      //           label: '异常',
      //           value: '1'
      //         }
      //       ],
      //       rules: [{
      //         required: true,
      //         message: '请选择对比状态',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '交易类型',
      //       prop: 'transType',
      //       type: 'select',
      //       slot: true,
      //       dicData: [
      //         {
      //           label: '支付',
      //           value: '1'
      //         },
      //         {
      //           label: '退款',
      //           value: '2'
      //         }
      //       ],
      //       rules: [{
      //         required: true,
      //         message: '请选择交易类型',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '能源平台订单数',
      //       prop: 'nyptTotal',
      //       rules: [{
      //         required: true,
      //         message: '请输入能源平台订单数',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '能源平台订单总金额',
      //       prop: 'nyptAmount',
      //       rules: [{
      //         required: true,
      //         message: '请输入能源平台订单总金额',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '银商订单数',
      //       prop: 'chinaumsTotal',
      //       rules: [{
      //         required: true,
      //         message: '请输入银商订单数',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '银商订单总金额',
      //       prop: 'chinaumsAmount',
      //       rules: [{
      //         required: true,
      //         message: '请输入银商订单总金额',
      //         trigger: 'blur'
      //       }]
      //     },
      //     {
      //       label: '利润',
      //       prop: 'profit',
      //       slot: true,
      //       formatter: (row) => {
      //         if (row.compareStatus === '0') {
      //           // 正常情况下，利润 = 支付 - 退款
      //           if (row.transType === '1') {
      //             return row.nyptAmount;
      //           } else {
      //             return -row.nyptAmount;
      //           }
      //         } else {
      //           // 异常情况下，分别显示能源平台和银商的利润
      //           if (row.transType === '1') {
      //             return `能源平台: ${row.nyptAmount}, 银商: ${row.chinaumsAmount}`;
      //           } else {
      //             return `能源平台: -${row.nyptAmount}, 银商: -${row.chinaumsAmount}`;
      //           }
      //         }
      //       }
      //     }
      //   ]
      // },
      // // 分页配置
      // page: {
      //   pageSize: 10,
      //   currentPage: 1,
      //   total: 0
      // },
      // 明细表格数据
      detailData: [],
      // 明细表格加载状态
      detailLoading: true,
      // 明细查询参数
      detailQuery: {},
      // 明细表格配置
      detailOption: {
        height: 'auto',
        calcHeight: 80,
        searchShow: true, // 显示筛选条件
        searchIcon: true, // 开启筛选条件隐藏
        searchIndex: 3, // 隐藏时显示的筛选条件数量
        searchMenuSpan: 6, // 按钮宽度
        searchShowBtn: false, // 默认折叠收起
        menu: false, //不显示操作菜单栏
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        dialogClickModal: false,
        rowClassName: ({ row }) => {
          return row.compareResult === '1' ? 'error-row' : '';
        },
        column: [
          {
            label: '充电订单号',
            headerAlign: "center",
            prop: 'mainOrderCode',
            search: true,
            searchOrder: 1,
            searchSpan: 6,
            searchLabelWidth: 90,
            minWidth: 100,
            rules: [{
              required: true,
              message: '请输入充电订单号',
              trigger: 'blur'
            }]
          },
          {
            label: '能源平台',
            headerAlign: "center",
            children: [
              {
                label: '三方订单号',
                headerAlign: "center",
                prop: 'nyptBusinessOrderNo',
                search: true,
                searchLabelWidth: 90,
                searchOrder: 2,
                minWidth: 100,
                rules: [{
                  required: true,
                  message: '请输入能源平台支付订单号',
                  trigger: 'blur'
                }]
              },
              {
                label: '交易时间',
                headerAlign: "center",
                prop: 'nyptTransDate',
                minWidth: 100,
                rules: [{
                  required: true,
                  message: '请输入能源平台交易时间',
                  trigger: 'blur'
                }]
              },
              {
                label: '交易金额',
                headerAlign: "center",
                prop: 'nyptPayAmount',
                minWidth: 80,
                rules: [{
                  required: true,
                  message: '请输入能源平台交易金额',
                  trigger: 'blur'
                }]
              }
            ]
          },
          {
            label: '对比结果',
            headerAlign: "center",
            prop: 'compareResult',
            type: 'select',
            search: true,
            searchOrder: 3,
            slot: true,
            minWidth: 100,
            dicData: [
              {
                label: '正常',
                value: '0'
              },
              {
                label: '异常',
                value: '1'
              }
            ],
            rules: [{
              required: true,
              message: '请选择对比结果',
              trigger: 'blur'
            }]
          },
          {
            label: '银商',
            headerAlign: "center",
            children: [
              {
                label: '银商订单号',
                headerAlign: "center",
                prop: 'chinaumsBusinessOrderNo',
                minWidth: 100,
                rules: [{
                  required: true,
                  message: '请输入银商支付订单号',
                  trigger: 'blur'
                }]
              },
              {
                label: '交易时间',
                headerAlign: "center",
                prop: 'chinaumsTransDate',
                minWidth: 100,
                rules: [{
                  required: true,
                  message: '请输入银商交易时间',
                  trigger: 'blur'
                }]
              },
              {
                label: '交易金额',
                headerAlign: "center",
                prop: 'chinaumsPayAmount',
                minWidth: 80,
                rules: [{
                  required: true,
                  message: '请输入银商交易金额',
                  trigger: 'blur'
                }]
              }
            ]
          },
          {
            label: '交易类型',
            prop: 'transType',
            type: 'select',
            search: true,
            searchOrder: 4,
            slot: true,
            width: 100,
            dicData: [
              {
                label: '支付',
                value: '1'
              },
              {
                label: '退款',
                value: '2'
              }
            ],
            rules: [{
              required: true,
              message: '请选择交易类型',
              trigger: 'blur'
            }]
          },
          // {
          //   label: '交易日期',
          //   prop: 'versionNo',
          //   width: 120,
          // },
          {
            label: '对账日期',
            prop: 'chargeDate',
            search: true,
            hide: true,
            searchRange: true,
            searchOrder: 7,
            display:false,
            searchSpan: 6,
            type: 'date',
            valueFormat: 'yyyy-MM-dd',
            searchValue: [tMinusOne, tMinusOne],
            rules: [{
              required: true,
              message: '请选择对账日期',
              trigger: 'blur'
            }]
          },
          {
            label: '运营商',
            prop: 'operatorId',
            type: 'select',
            search: true,
            searchOrder: 5,
            // display: false,
            dicData: [],
            rules: [{
              required: true,
              message: '请选择运营商',
              trigger: 'blur'
            }]
          },
          // {
          //   label: '支付渠道',
          //   prop: 'fundsFrom',
          //   type: 'select',
          //   search: true,
          //   searchOrder: 6,
          //   dicData: [],
          //   rules: [{
          //     required: true,
          //     message: '请选择支付渠道',
          //     trigger: 'blur'
          //   }]
          // },
          {
            label: "充电站",
            prop: "chargeStationName",
            search: true,
            span: 24,
            searchOrder: 4,
            width: 150,
            searchPlaceholder: "请输入充电站名称"
          },
        ]
      },
      // 明细分页配置
      detailPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 当前选中的结果行
      currentRow: null,
      // 重试对账弹窗显示状态
      retryVisible: false,
      // 重试对账表单
      retryForm: {
        compareType: COMPARE_TYPE.NYPT_CHINAUMS, // 对比类型：5-能源平台与银商账单比对
        startDate: this.formatDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
        endDate: this.formatDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd')
      },
      // 重试进度
      retryLoading: false,
      retryProgress: 0,
      retryCurrentStep: 0,
      retryTotalStep: 0,
      retryCancelFlag: false,
      retryResultSummary: null,
      retryLogs: [],

      // 每日列表数据
      dailyData: [],
      dailyLoading: false,
      dailyPage: { pageSize: 10, currentPage: 1, total: 0 },
      dailyQuery: {},
      dailySelection: [],
      dailyOption: {
        height: 'auto',
        calcHeight: 80,
        selection: true,
        searchShow: true,
        border: true,
        index: false,
        menu: false,
        column: [
          { label: '对账日期', prop: 'chargeDate', type: 'date', valueFormat: 'yyyy-MM-dd', search: true, searchRange: true },
          { label: '交易类型', prop: 'transType', type: 'select', search: true, dicData: [ { label: '支付', value: '1' }, { label: '退款', value: '2' } ] },
          { label: '执行状态', prop: 'status', slot: true, width: 100 },
          { label: '对比状态', prop: 'compareStatus', type: 'select', search: true, slot: true, dicData: [ { label: '正常', value: '0' }, { label: '异常', value: '1' } ] },
          { label: '异常数', prop: 'abnormalCount', width: 100 },
          { label: '能源订单数', prop: 'nyptTotal', width: 120 },
          { label: '银商订单数', prop: 'chinaumsTotal', width: 120 },
          { label: '支付渠道', prop: 'fundsFrom', type: 'select', search: true },
          { label: '运营商', prop: 'operatorId', type: 'select', search: true },
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList () {
      return {
        retryBtn: this.vaildData(this.permission.chinaums_retry, false),
        exportBtn: this.vaildData(this.permission.chinaums_export, false),
      };
    },

    showchargeDate() {
      console.log("showchargeDate:", this.detailQuery)
      // 确保日期范围逻辑正确
      if (this.detailQuery.chargeDateStart && this.detailQuery.chargeDateEnd) {
        // 返回格式化的日期范围
        return "（" + this.detailQuery.chargeDateStart + " 至 " + this.detailQuery.chargeDateEnd + "）";
      }
      const yesterday = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
      const yesterdayStr = this.formatDate(yesterday, 'yyyy-MM-dd');
      return "(" + yesterdayStr + " 至 " + yesterdayStr + ")"
    },
    // nyptPayMismatch() {
    //   return this.summaryData.nyptPayTotal !== this.summaryData.chinaumsPayTotal;
    // },
    // nyptRefundMismatch() {
    //   return this.summaryData.nyptRefundTotal !== this.summaryData.chinaumsRefundTotal;
    // },
    // nyptProfitMismatch() {
    //   return this.summaryData.nyptProfit !== this.summaryData.chinaumsProfit;
    // },
    // chinaumsPayMismatch() {
    //   return this.summaryData.chinaumsPayTotal !== this.summaryData.nyptPayTotal;
    // },
    // chinaumsRefundMismatch() {
    //   return this.summaryData.chinaumsRefundTotal !== this.summaryData.nyptRefundTotal;
    // },
    // chinaumsProfitMismatch() {
    //   return this.summaryData.chinaumsProfit !== this.summaryData.nyptProfit;
    // }
  },
  created() {
    this.getPaymentReceivers();
    this.getOperators();
    this.detailQuery.chargeDateStart = this.formatDate(tMinusOne, 'yyyy-MM-dd');
    this.detailQuery.chargeDateEnd = this.formatDate(tMinusOne, 'yyyy-MM-dd');
  },
  methods: {
    // 每日对账分页加载
    onDailyLoad() {
      this.fetchDailyPage();
    },
    dailyCurrentChange(page){
      this.dailyPage.currentPage = page;
      this.fetchDailyPage();
    },
    dailySizeChange(size){
      this.dailyPage.pageSize = size;
      this.fetchDailyPage();
    },
    dailyRefreshChange(){
      this.fetchDailyPage();
    },
    dailySearchChange(params,done){
      this.dailyQuery = { ...params };
      this.dailyPage.currentPage = 1;
      this.fetchDailyPage().finally(()=>done && done());
    },
    dailySearchReset(){
      this.dailyQuery = {};
      this.dailyPage.currentPage = 1;
      this.fetchDailyPage();
    },
    dailySelectionChange(list){
      this.dailySelection = list || [];
      // 自动根据勾选回填日期范围
      if(this.dailySelection.length){
        const dates = this.dailySelection.map(i=>i.chargeDate).sort();
        this.retryForm.startDate = dates[0];
        this.retryForm.endDate = dates[dates.length-1];
      }
    },
    fetchDailyPage(){
      this.dailyLoading = true;
      const params = { ...this.dailyQuery };
      return getDailyComparePage(this.dailyPage.currentPage, this.dailyPage.pageSize, params)
        .then(res=>{
          const { data } = res;
          this.dailyData = data?.records || data?.data || [];
          this.dailyPage.total = data?.total || 0;
        })
        .finally(()=>{
          this.dailyLoading = false;
        });
    },
    getQueryParams(params){
      // 合并查询参数
      const queryParams = Object.assign({}, params, this.detailQuery);
      // 设置默认查询日期为昨天(T-1)
      // if (!params.chargeDate) {
      //   const yesterday = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
      //   const yesterdayStr = this.formatDate(yesterday, 'yyyy-MM-dd');
      //   queryParams.chargeDateStart = yesterdayStr;
      //   queryParams.chargeDateEnd = yesterdayStr;
      // } else 
      if (params.chargeDate && params.chargeDate.length == 2) {
        queryParams.chargeDateStart = this.formatDate(new Date(params.chargeDate[0]), 'yyyy-MM-dd');
        queryParams.chargeDateEnd = this.formatDate(new Date(params.chargeDate[1]), 'yyyy-MM-dd');
        delete queryParams.chargeDate;
      }
      this.detailQuery = JSON.parse(JSON.stringify(params))
      this.detailQuery.chargeDateStart = queryParams.chargeDateStart;
      this.detailQuery.chargeDateEnd = queryParams.chargeDateEnd;

      if (params.compareResult){
        queryParams.compareStatus = params.compareResult
      }
      console.log("queryParams:", queryParams, this.detailQuery)
      return queryParams
    },
    // 表格数据加载
    onLoad(page, params = {}) {
      this.detailLoading = true;
      console.log("onload:params", params)
      // 一次性获取汇总和明细数据
      getListWithDetails(page.currentPage, page.pageSize, this.getQueryParams(params))
        .then(res => {
          const data = res.data.data;
          
          // 计算汇总数据
          // this.calculateSummary(data.summaryRecords || []);

          this.summaryData = data.summaryData || {};

          // 处理明细数据
          this.detailPage.total = data.detailTotal || 0;
          this.detailData = data.detailRecords || [];
          this.detailLoading = false;
        }).catch(error => {
          this.detailLoading = false;
          console.error('加载数据失败:', error);
        });
    },
    // 明细搜索条件变化
    detailSearchChange(params, done) {
      this.detailQuery = params;
      this.detailPage.currentPage = 1;
      this.onLoad(this.detailPage, params);
      done();
    },
    
    // 明细搜索重置
    detailSearchReset() {
      this.detailQuery = {};
      this.onLoad(this.detailPage);
    },
    // 选择项变化
    // selectionChange(list) {
    //   this.selectionList = list;
    // },
    // // 当前页变化
    // currentChange(currentPage) {
    //   this.page.currentPage = currentPage;
    // },
    // // 页大小变化
    // sizeChange(pageSize) {
    //   this.page.pageSize = pageSize;
    // },
    // // 刷新
    // refreshChange() {
    //   this.onLoad(this.page, this.query);
    // },
    // 明细当前页变化
    detailCurrentChange(currentPage) {
      this.detailPage.currentPage = currentPage;
    },
    // 明细页大小变化
    detailSizeChange(pageSize) {
      this.detailPage.pageSize = pageSize;
    },
    // 明细刷新
    detailRefreshChange() {
      this.onLoad(this.detailPage, this.detailQuery);
    },
    // 行点击事件
    // rowClick(row) {
    //   this.currentRow = row;
    //   this.detailVisible = true;
    //   this.detailPage.currentPage = 1;
    //   this.onDetailLoad(this.detailPage);
    // },
    // 明细弹窗关闭
    // handleDetailClose() {
    //   this.detailVisible = false;
    //   this.currentRow = null;
    //   this.detailData = [];
    // },
    // 重试对账
    handleRetry() {
      this.retryVisible = true;
      // 设置默认日期为当前查询的日期范围
      if (this.detailQuery && this.detailQuery.chargeDate) {
        if (Array.isArray(this.detailQuery.chargeDate)) {
          this.retryForm.startDate = this.detailQuery.chargeDate[0];
          this.retryForm.endDate = this.detailQuery.chargeDate[1];
        } else {
          this.retryForm.startDate = this.detailQuery.chargeDate;
          this.retryForm.endDate = this.detailQuery.chargeDate;
        }
      } else {
        // 默认为昨天
        const yesterday = this.formatDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
        this.retryForm.startDate = yesterday;
        this.retryForm.endDate = yesterday;
      }
    },
    // 重试对账弹窗关闭
    handleRetryClose() {
      this.retryVisible = false;
    },
    // 确认重试对账
    async confirmRetry() {
      if (!this.retryForm.startDate || !this.retryForm.endDate) {
        this.$message.warning('请选择开始和结束日期');
        return;
      }
      this.retryLoading = true;
      this.retryProgress = 0;
      this.retryCurrentStep = 0;
      this.retryTotalStep = this.calcDays(this.retryForm.startDate, this.retryForm.endDate);
      this.retryCancelFlag = false;

      try {
        const res = await retry(this.retryForm);
        if(res.data.code == 200){
          this.$message.success(res.data.msg || '已提交重试任务');
          // 启动轮询状态
          this.startRetryPolling();
        }else{
          this.$message.warning(res.data.msg || '重试提交失败');
          this.retryLoading = false;
        }
      } catch (e) {
        this.retryLoading = false;
      }
    },

    // 计算日期天数
    calcDays(start, end){
      const s = new Date(start);
      const e = new Date(end);
      return Math.floor((e - s) / (24*60*60*1000)) + 1;
    },

    // 启动轮询进度（示例：每2秒刷新一次，需后端配合提供进度接口）
    startRetryPolling(){
      // 这里假设使用 /compare-bill/page 数据中按日期状态估算进度
      const timer = setInterval(async ()=>{
        if(this.retryCancelFlag){
          clearInterval(timer);
          this.retryLoading = false;
          return;
        }
        await this.fetchDailyPage();
        // 根据每日列表中位于所选日期范围内、状态为已处理(成功/失败)的天数估算进度
        const inRange = (d)=> d >= this.retryForm.startDate && d <= this.retryForm.endDate;
        const rowsInRange = (this.dailyData || []).filter(i=> inRange(i.chargeDate));
        const doneDays = rowsInRange.filter(i=> i.status === '2' || i.status === '3').length;
        this.retryCurrentStep = doneDays;
        this.retryProgress = Math.min(100, Math.floor(doneDays*100/this.retryTotalStep));
        if(this.retryProgress >= 100){
          clearInterval(timer);
          this.retryLoading = false;
          // 生成结果摘要
          const successDays = rowsInRange.filter(i=> i.status === '2').length;
          const failDays = rowsInRange.filter(i=> i.status === '3').length;
          const stillAbnormal = rowsInRange.reduce((sum,i)=> sum + (Number(i.abnormalCount||0)), 0);
          this.retryResultSummary = {
            totalDays: this.retryTotalStep,
            successDays,
            failDays,
            stillAbnormal
          };
          this.$message.success('重试完成');
          // 刷新明细和每日列表
          this.detailRefreshChange();
          this.fetchDailyPage();
        }
      }, 2000);
    },
    // 导出数据
    handleExport() {
      this.$confirm('确认导出当前筛选的对账数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log("确定导出")
        exportData(this.getQueryParams(this.detailQuery))
        .then((response) => {
          const blob = response.data; // 获取返回的 Blob 数据
          let filename = `对账明细${this.showchargeDate} .xlsx`; // 设置默认的文件名
          // 创建一个 Blob URL 并触发下载
          const downloadUrl = window.URL.createObjectURL(new Blob([blob])); // 使用 Blob 创建 URL
          const a = document.createElement("a");
          a.href = downloadUrl;
          a.download = filename; // 使用从 headers 中提取的文件名
          document.body.appendChild(a); // 将元素添加到 DOM 中
          a.click(); // 模拟点击下载
          document.body.removeChild(a); // 下载后移除元素
          // this.exportLoading = false;
        })
        .catch((err) => {
          this.$message.error('导出失败: ' + (err.message || '未知错误'));
          // console.error("导出失败:", err);
          // this.exportLoading = false;
        });

        // .then(res => {
        //   this.$message.success('导出成功');
        //   console.log("导出:", res.data)
        //   window.open(res.data.data.filePath);
        // }).catch(error => {
        //   this.$message.error('导出失败: ' + (error.message || '未知错误'));
        // });
      }).catch(() => {});
    },
    // // 表单打开前
    // beforeOpen(done, type) {
    //   done();
    // },
    // // 行保存
    // rowSave(row, done, loading) {
    //   done();
    // },
    // // 行更新
    // rowUpdate(row, index, done, loading) {
    //   done();
    // },
    // // 行删除
    // rowDel(row) {
    // },
    // 计算汇总数据
    // calculateSummary(records) {
    //   console.log('calculateSummary:', records)
    //   // 重置汇总数据
    //   this.summaryData = {
    //     payTotal: 0,
    //     refundTotal: 0,
    //     profit: 0,
    //     nyptPayTotal: 0,
    //     nyptRefundTotal: 0,
    //     nyptProfit: 0,
    //     chinaumsPayTotal: 0,
    //     chinaumsRefundTotal: 0,
    //     chinaumsProfit: 0,
    //     hasException: false
    //   };
      
    //   if (!records || records.length === 0) {
    //     // 如果没有记录，则格式化为0.00并返回
    //     this.formatSummaryData();
    //     return;
    //   }
      
    //   // 检查是否有异常记录
    //   const hasException = records.some(record => record.compareStatus === '1');
    //   this.summaryData.hasException = hasException;
      
    //   // 计算汇总数据
    //   records.forEach(record => {
    //     if (record.transType === '1') { // 支付
    //       this.summaryData.payTotal += parseFloat(record.nyptAmount || 0);
    //       this.summaryData.profit += parseFloat(record.nyptAmount || 0);
          
    //       this.summaryData.nyptPayTotal += parseFloat(record.nyptAmount || 0);
    //       this.summaryData.nyptProfit += parseFloat(record.nyptAmount || 0);
          
    //       this.summaryData.chinaumsPayTotal += parseFloat(record.chinaumsAmount || 0);
    //       this.summaryData.chinaumsProfit += parseFloat(record.chinaumsAmount || 0);
    //     } else if (record.transType === '2') { // 退款
    //       this.summaryData.refundTotal += parseFloat(record.nyptAmount || 0);
    //       this.summaryData.profit -= parseFloat(record.nyptAmount || 0);
          
    //       this.summaryData.nyptRefundTotal += parseFloat(record.nyptAmount || 0);
    //       this.summaryData.nyptProfit -= parseFloat(record.nyptAmount || 0);
          
    //       this.summaryData.chinaumsRefundTotal += parseFloat(record.chinaumsAmount || 0);
    //       this.summaryData.chinaumsProfit -= parseFloat(record.chinaumsAmount || 0);
    //     }
    //   });
      
    //   this.formatSummaryData();
    // },
    
    // // 格式化汇总数据
    // formatSummaryData() {
    //   // 格式化数字，保留两位小数
    //   this.summaryData.payTotal = parseFloat(this.summaryData.payTotal).toFixed(2);
    //   this.summaryData.refundTotal = parseFloat(this.summaryData.refundTotal).toFixed(2);
    //   this.summaryData.profit = parseFloat(this.summaryData.profit).toFixed(2);
    //   this.summaryData.nyptPayTotal = parseFloat(this.summaryData.nyptPayTotal).toFixed(2);
    //   this.summaryData.nyptRefundTotal = parseFloat(this.summaryData.nyptRefundTotal).toFixed(2);
    //   this.summaryData.nyptProfit = parseFloat(this.summaryData.nyptProfit).toFixed(2);
    //   this.summaryData.chinaumsPayTotal = parseFloat(this.summaryData.chinaumsPayTotal).toFixed(2);
    //   this.summaryData.chinaumsRefundTotal = parseFloat(this.summaryData.chinaumsRefundTotal).toFixed(2);
    //   this.summaryData.chinaumsProfit = parseFloat(this.summaryData.chinaumsProfit).toFixed(2);
    // },
    getOperators() {
      getOperatorList(1, 1000, {}).then(res => {
        this.operators = res.data.data.records || [];
        // 填充运营商下拉选项
        const operatorColumn = this.findObject(this.detailOption.column, "prop", "operatorId");
        if (operatorColumn) {
          operatorColumn.dicData = this.operators.map(item => ({
            label: item.operatorName,
            value: item.operatorId
          }));
        }
        console.log("getOperatorList:", this.detailOption.column)
      });
    },
    getPaymentReceivers() {
      getPaymentReceiverList(1, 1000, {}).then(res => {
        const data = res.data.data;
        this.paymentReceivers = data.records;
        const column = this.findObject(this.detailOption.column, "prop", "fundsFrom");
        if (column) {
          column.dicData = data.records.map(item => {
            return {
              // label: `${item.companyName} - ${item.fundsFrom}`,
              label: `${item.companyName}`,
              value: item.fundsFrom
            };
          });
        }
        console.log("getPaymentReceiverList:", this.detailOption.column)
      });
    },
    findObject(array, key, value) {
      for (let i = 0; i < array.length; i++) {
        if (array[i][key] === value) {
          return array[i];
        }
      }
      return null;
    },
    formatDate(date, format) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      // 替换格式字符串
      return format
        .replace('yyyy', year)
        .replace('MM', month)
        .replace('dd', day);
    },
  }
};
</script>

<style>
.error-row {
  background-color: #fef0f0;
}

.summary-card {
  margin-bottom: 20px;
  background-color: aliceblue;
}

.summary-content {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.summary-item {
  margin-right: 30px;
  margin-bottom: 10px;
}

.summary-item .label {
  font-weight: bold;
  margin-right: 5px;
}

.header-buttons {
  float: right;
  margin-top: -5px;
}

.highlight {
  color: #ff0000;
  font-weight: bold;
  /* animation: blink 1s infinite; */
}
/* @keyframes blink {
  50% { opacity: 0.5; }
} */

</style>