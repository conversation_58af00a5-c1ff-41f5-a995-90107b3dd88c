/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.controller;

import org.springblade.system.dto.RetryCompareDTO;
import org.springblade.system.entity.CompareBillResult;
import org.springblade.system.service.ICompareBillDetailService;
import org.springblade.system.service.ICompareBillResultService;
import org.springblade.system.vo.CompareBillDetailVO;
import org.springblade.system.vo.CompareBillResultVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对账结果表 控制器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@RestController
@RequestMapping("/compare-bill")
@Slf4j
public class CompareBillResultController extends BladeController {

	@Autowired
	private ICompareBillResultService compareBillResultService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<CompareBillResult> detail(CompareBillResult compareBillResult) {
		log.info("对账结果表详情接口，开始。入参："+compareBillResult.toString());
		CompareBillResult detail = compareBillResultService.getOne(Condition.getQueryWrapper(compareBillResult));
		log.info("对账结果表详情接口结束");
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	public R<IPage<CompareBillResultVO>> page(CompareBillResultVO compareBillResult, Query query) {
		log.info("对账结果表列表分页接口，开始。入参："+query.toString());
		IPage<CompareBillResultVO> pages = compareBillResultService.selectCompareBillResultPage2(Condition.getPage(query), compareBillResult);
		log.info("对账结果表列表分页接口结束");
		return R.data(pages);
	}

	/**
	 * 重试对账
	 */
	@PostMapping("/retry")
	public R<Boolean> retry(@RequestBody RetryCompareDTO retryCompareDTO) {
		log.info("重试对账接口，开始。入参："+retryCompareDTO.toString());
		boolean result = compareBillResultService.retry(retryCompareDTO);
		log.info("重试对账接口结束");
		return R.data(result);
	}

	/**
	 * 银商对账重试接口
	 * 传入重试时间段（默认T-1），查找该时间内所有对比失败的记录，根据订单号重新对比
	 */
	@PostMapping("/retry-failed")
	public R<Boolean> retryFailedRecords(@RequestBody RetryCompareDTO retryCompareDTO) {
		log.info("银商对账重试接口，开始。入参：{}", retryCompareDTO.toString());

		// 如果没有传入日期，默认使用T-1（昨天）
		if (retryCompareDTO.getStartDate() == null || retryCompareDTO.getStartDate().isEmpty()) {
			String yesterday = DateUtil.format(DateUtil.plusDays(new Date(), -1), "yyyy-MM-dd");
			retryCompareDTO.setStartDate(yesterday);
		}
		if (retryCompareDTO.getEndDate() == null || retryCompareDTO.getEndDate().isEmpty()) {
			retryCompareDTO.setEndDate(retryCompareDTO.getStartDate());
		}

		// 如果没有传入对比类型，默认使用能源平台与银商账单比对
		if (retryCompareDTO.getCompareType() == null || retryCompareDTO.getCompareType().isEmpty()) {
			retryCompareDTO.setCompareType("5"); // 能源平台与银商账单比对
		}

		boolean result = compareBillResultService.retryFailedRecords(retryCompareDTO);
		log.info("银商对账重试接口结束，结果：{}", result);
		return R.data(result);
	}


}
